<template>
  <div class="card flex justify-content-center shadow p-2 gap-3 rounded">
    <AutoComplete
      v-if="isShow"
      v-model="selected"
      :suggestions="data"
      :multiple="true"
      :delay="400"
      @complete="onChangeFilterHandler"
      @change="onChangeHandler"
      @clear="refetch"
      optionLabel="order_id"
      placeholder="Заказы"
      class="w-full md:w-80"
      dataKey="order_id"
      minLength="2"
      dropdown
      dropdownMode="current"
      :loading="isLoading"
    >
      <template #option="slotProps">
        <div class="flex align-items-center">
          <div :class="slotProps.option?.order_gtd ? 'text-green-500' : 'text-red-600'">
            <span class="font-bold"> {{ slotProps.option?.order_id }} </span> - {{ slotProps.option?.client?.client_name }}
          </div>
        </div>
      </template>
      <!-- <template #value="slotProps">
        <div class="flex items-center">
          {{ slotProps.value.map((i) => i.order_id).join(', ') }}
        </div>
      </template> -->
      <template #chip="slotProps">
        <div :class="slotProps.value?.order_gtd ? 'text-green-500 font-sembold' : 'text-red-600 font-bold'">{{ slotProps.value?.order_id }}</div>
      </template>
    </AutoComplete>
    <!-- <Button label="Сброс" @click="reset" /> -->
  </div>
</template>

<script setup>
import { trpc } from '@/tRPC'
import { useQuery } from '@tanstack/vue-query'
import AutoComplete from 'primevue/autocomplete'
import Button from 'primevue/button'
import { ref, watch } from 'vue'

const props = defineProps({
  onSelect: {
    type: Function,
    default: () => {}
  }
})

const emit = defineEmits(['update:selected'])

const selected = ref()
const searchValue = ref()
const options = ref([])
const isShow = ref(true)

// watch(searchValue, (value) => {
//   //console.log('🚀 ~ watch ~ value:', value)
//   if (!value) {
//     refetch()
//   }
//   //console.log('🚀 ~ watch ~ refetch:')
// })

const { isLoading, data, refetch } = useQuery({
  queryFn: async () => {
    const res = await trpc.services.orderList.query({ searchValue: searchValue.value, limit: 10 })
    options.value = res
    return res
  },
  queryKey: [searchValue],
  refetchOnMount: true,
  refetchOnWindowFocus: false,
  staleTime: 3000,
  enabled: true
})

function onChangeFilterHandler(e) {
  searchValue.value = e.query
}

function onChangeHandler(e) {
  //console.log('onChangeHandler:', e)

  props.onSelect?.(e)
  emit('update:selected', e)
}

function reset() {
  refetch()
  isShow.value = false
  isShow.value = true
}
</script>
