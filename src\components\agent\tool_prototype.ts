import { createTool } from "@mastra/core/tools";
import { z } from "zod";

// Схема для входных данных - универсальное значение для поиска (название или ИНН)
export const FindOrgInputSchema = z.object({
    query: z.string().describe("Название организации или ИНН для поиска реквизитов"),
});
export type FindOrgInput = z.infer<typeof FindOrgInputSchema>;

// Схема для ответа API с реквизитами организации
export const OrgDetailsSchema = z.object({
    name: z.string().optional().describe("Полное наименование организации"),
    inn: z.string().optional().describe("ИНН организации"),
    kpp: z.string().optional().describe("КПП организации"),
    ogrn: z.string().optional().describe("ОГРН организации"),
    address: z.string().optional().describe("Юридический адрес организации"),
    director: z.string().optional().describe("Руководитель организации"),
    phone: z.string().optional().describe("Телефон организации"),
    email: z.string().optional().describe("Email организации"),
    // website: z.string().optional().describe("Веб-сайт организации"),
    // okved: z.string().optional().describe("Основной ОКВЭД"),
    // status: z.string().optional().describe("Статус организации"),
    // registrationDate: z.string().optional().describe("Дата регистрации"),
    // capital: z.string().optional().describe("Уставный капитал"),
});
export type OrgDetails = z.infer<typeof OrgDetailsSchema>;

// Схема для полного ответа API
export const FindOrgResponseSchema = z.object({
    success: z.boolean().describe("Успешность запроса"),
    data: OrgDetailsSchema.describe("Данные организации"),
    message: z.string().optional().describe("Сообщение об ошибке или статусе"),
});
export type FindOrgResponse = z.infer<typeof FindOrgResponseSchema>;

export const findOrgTool = createTool({
    id: "find_organization_details",
    inputSchema: FindOrgInputSchema,
    outputSchema: FindOrgResponseSchema,
    description: `Поиск всех реквизитов организации по названию или ИНН. Возвращает ИНН, КПП, ОГРН, адрес, руководителя и другую информацию об организации.`,
    execute: async ({ context }) => {
        try {
            // Валидация входных данных
            if (!context.query || context.query.trim().length === 0) {
                console.error("❌ Пустое название или ИНН организации");
                return {
                    success: false,
                    message: "Необходимо указать название или ИНН организации",
                    data: {}
                } as FindOrgResponse;
            }

            const result = await findOrganization(context.query.trim());
            return result as FindOrgResponse;
        } catch (error) {
            console.error("❌ Критическая ошибка в findOrgTool:", error);
            return {
                success: false,
                message: `Ошибка при поиске организации: ${error instanceof Error ? error.message : 'Неизвестная ошибка'}`,
                data: {}
            } as FindOrgResponse;
        }
    },
});

// Функция для извлечения только нужных полей из ответа API
function extractOrgData(apiData: any): OrgDetails {
    const data = apiData.data || apiData;

    return {
        name: data.name?.short_with_opf || data.name?.full_with_opf || undefined,
        inn: data.inn || undefined,
        kpp: data.kpp || undefined,
        ogrn: data.ogrn || undefined,
        address: data.address?.value || data.address?.unrestricted_value || undefined,
        director: data.management?.name || undefined,
        phone: data.phones?.[0]?.value || undefined,
        email: data.emails?.[0]?.value || undefined,
    };
}

// queryValue может быть названием или ИНН
async function findOrganization(queryValue: string): Promise<FindOrgResponse> {
    try {
        // Кодируем значение для URL
        const encodedValue = encodeURIComponent(queryValue);
        const apiUrl = `https://mirsalnikov.ru/api/service/findorg/${encodedValue}`;

        console.log("🔍 Поиск организации по значению:", queryValue);
        console.log("📡 URL запроса:", apiUrl);

        const response = await fetch(apiUrl, {
            method: "GET",
            headers: {
                "accept": "*/*",
                "accept-language": "ru-RU,ru;q=0.9,en-GB;q=0.8,en;q=0.7,en-US;q=0.6,es;q=0.5",
                "content-type": "application/json",
                "priority": "u=1, i",
                "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                "sec-ch-ua-mobile": "?0",
                "sec-ch-ua-platform": '"Linux"',
                "sec-fetch-dest": "empty",
                "sec-fetch-mode": "cors",
                "sec-fetch-site": "same-origin",
                // Убираем cookie для безопасности - они могут быть специфичными для сессии
            },
            // Добавляем timeout для предотвращения зависания
            signal: AbortSignal.timeout(15000) // 15 секунд для поиска
        });

        console.log("📡 Статус ответа:", response.status);

        if (!response.ok) {
            console.error(`❌ HTTP ошибка при поиске организации: ${response.status} ${response.statusText}`);
            return {
                data: {},
                success: false,
                message: `Ошибка сервера при поиске: ${response.status} ${response.statusText}`,
            };
        }

        const data = await response.json();
        console.log("🚀 ~ findOrganization ~ data:", data);

        // Валидация структуры ответа
        if (!data || typeof data !== 'object') {
            console.error("❌ Некорректная структура ответа от API поиска организации");
            return {
                success: false,
                data: {},
                message: "Получен некорректный ответ от сервера поиска",
            };
        }

        // Обрабатываем ответ в зависимости от структуры API
        // Предполагаем, что API возвращает массив с информацией об организации
        if (Array.isArray(data) && data.length > 0) {
            const orgData = data[0]; // Берем первую найденную организацию

            // Извлекаем только нужные поля согласно схеме
            const filteredData = extractOrgData(orgData);

            // Дополнительная валидация данных организации
            if (!filteredData.name && !filteredData.inn) {
                console.error("❌ Найденная организация не содержит основных данных");
                return {
                    success: false,
                    data: {},
                    message: "Найденная организация содержит неполные данные",
                };
            }

            console.log(`✅ Организация найдена: ${filteredData.name || 'Без названия'} (ИНН: ${filteredData.inn || 'Не указан'})`);
            return {
                success: true,
                data: filteredData,
                message: "Данные организации успешно получены",
            };
        } else {
            console.log(`ℹ️ Организация "${queryValue}" не найдена`);
            return {
                success: false,
                data: {},
                message: `Организация "${queryValue}" не найдена в базе данных`,
            };
        }

    } catch (error) {
        console.error("❌ Ошибка при поиске организации:", error);

        if (error instanceof Error) {
            if (error.name === 'AbortError') {
                return {
                    success: false,
                    data: {},
                    message: "Превышено время ожидания при поиске организации",
                };
            }

            if (error.message.includes('fetch')) {
                return {
                    success: false,
                    data: {},
                    message: "Ошибка сети при поиске организации. Проверьте подключение к интернету",
                };
            }
        }

        return {
            success: false,
            data: {},
            message: `Ошибка при выполнении запроса: ${error instanceof Error ? error.message : 'Неизвестная ошибка'}`,
        };
    }
}


const apiURL = process.env.DOC_API_URL;

// Универсальный парсер: если пришла строка с JSON — распарсить.
// Дополнительно пытаемся конвертировать одинарные кавычки в двойные для псевдо-JSON.
function parseJsonIfString<T = unknown>(value: unknown): unknown {
    if (typeof value !== "string") return value;
    try {
        return JSON.parse(value);
    } catch {
        try {
            const normalized = value.replace(/'/g, '"');
            return JSON.parse(normalized);
        } catch {
            return value;
        }
    }
}

export const ClientSchema = z.object({
    fullorgname: z.string().default("неизвестно").describe("Полное наименование организации"),
    client_name: z.string().default("неизвестно").describe("ФИО контактного лица"),
    client_mail: z.string().default("неизвестно").describe("E-mail контактного лица"),
    client_phone: z.string().default("неизвестно").describe("Телефон контактного лица"),
    client_country: z
        .string()
        .optional()
        .default("Россия")
        .describe("Страна, по умолчанию: Россия"),
    client_city: z.string().optional().default("неизвестно").describe("Город"),
    client_street: z.string().optional().default("неизвестно").describe("Улица"),
    client_house: z.string().optional().default("неизвестно").describe("Дом"),
    client_postindex: z.string().optional().default("неизвестно").describe("Почтовый Индекс"),
});
export type Client = z.infer<typeof ClientSchema>;

export const OrgSchema = z.object({
    org_name: z.string().default("неизвестно").describe("Название организации"),
    org_adress: z.string().optional().default("неизвестно").describe("Адрес организации"),
    org_inn: z.string().optional().default("неизвестно").describe("ИНН организации"),
    org_kpp: z.string().optional().default("неизвестно").describe("КПП организации"),
    org_rschet: z.string().optional().default("неизвестно").describe("Расчетный счет организации"),
    org_kschet: z
        .string()
        .optional()
        .default("неизвестно")
        .describe("Корреспондентский счет организации"),
    org_bik: z.string().optional().default("неизвестно").describe("БИК банка организации"),
    org_bank: z.string().optional().default("неизвестно").describe("Банк организации"),
});
export type Org = z.infer<typeof OrgSchema>;

export const ProductSchema = z.object({
    prod_sku: z.string(),
    prod_analogsku: z.string(),
    prod_price: z.number(),
    prod_manuf: z.string(),
    prod_type: z.string(),
    prod_size: z.string(),
    prod_purpose: z.string(),
    //   qty: z.number(),
});
export type Product = z.infer<typeof ProductSchema>;

export const ProductDatumSchema = z.object({
    id: z.number(),
    qty: z.number(),
    product: ProductSchema,
});
export type ProductDatum = z.infer<typeof ProductDatumSchema>;

// Входные поля с предобработкой для устойчивости к строковым значениям
const ProductDataInputSchema = z.preprocess(
    (val) => parseJsonIfString<ProductDatum[]>(val),
    z.array(ProductDatumSchema)
);

const ClientInputSchema = z.preprocess(
    (val) => parseJsonIfString<Client>(val),
    ClientSchema
);

const OrgInputSchema = z.preprocess(
    (val) => parseJsonIfString<Org>(val),
    OrgSchema
);

export const OfferPayloadSchema = z.object({
    countryId: z.coerce
        .number()
        .optional()
        .default(643)
        .describe("ID страны, по умолчанию: 643 (Россия)"),
    //   filename: z.string().default("99_demo_schet_ip_igolkin.xlsx").optional(),
    shippingIndex: z.coerce.number().default(101000).optional(),
    //   shippingPrice: z.null().optional(),
    shippingType: z.enum(["express", "standard"]).default("express").optional(),
    productData: ProductDataInputSchema,
    client: ClientInputSchema,
    org: OrgInputSchema,
});
export type OfferPayload = z.infer<typeof OfferPayloadSchema>;

export const OfferResponseSchema = z.object({
    success: z.boolean(),
    message: z.string(),
    files: z.object({
        xlsx: z.object({
            url: z.string(),
            fileName: z.string(),
        }),
        pdf: z
            .object({
                url: z.string(),
                fileName: z.string(),
            })
            .nullable(),
    }),
});
export type OfferResponse = z.infer<typeof OfferResponseSchema>;

export const generateDocTool = createTool({
    id: "generate xlsx document",
    inputSchema: OfferPayloadSchema,
    outputSchema: OfferResponseSchema,
    description: `generate xlsx document by data`,
    execute: async ({ context }) => {
        try {
            if (!apiURL) {
                console.error("❌ DOC_API_URL не установлен");
                return {
                    success: false,
                    message: "DOC_API_URL не установлен. Укажите переменную окружения DOC_API_URL",
                    files: {
                        xlsx: {
                            url: "#",
                            fileName: "error.xlsx"
                        },
                        pdf: null
                    }
                } as OfferResponse;
            }

            // Валидация входных данных
            if (!context.productData || context.productData.length === 0) {
                console.error("❌ Нет товаров для создания счета");
                return {
                    success: false,
                    message: "Невозможно создать счет: отсутствуют товары",
                    files: {
                        xlsx: {
                            url: "#",
                            fileName: "error.xlsx"
                        },
                        pdf: null
                    }
                } as OfferResponse;
            }

            const result = await generateDoc(context);
            return result as OfferResponse;
        } catch (error) {
            console.error("❌ Критическая ошибка в generateDocTool:", error);
            return {
                success: false,
                message: `Ошибка при создании документа: ${error instanceof Error ? error.message : 'Неизвестная ошибка'}`,
                files: {
                    xlsx: {
                        url: "#",
                        fileName: "error.xlsx"
                    },
                    pdf: null
                }
            } as OfferResponse;
        }
    },
});
async function generateDoc(payload: OfferPayload): Promise<OfferResponse> {
    if (!apiURL) {
        console.error("❌ DOC_API_URL не установлен");
        return {
            success: false,
            message: "DOC_API_URL не установлен",
            files: {
                xlsx: {
                    url: "#",
                    fileName: "error.xlsx"
                },
                pdf: null
            }
        };
    }

    try {
        const input = JSON.stringify({
            ...payload,
            filename: "99_demo_schet_ip_igolkin.xlsx",
            fileResponse: true,
        });

        console.log("🚀 ~ generateDoc ~ input:", input);

        const res = await fetch(`${apiURL}`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                "Accept": "application/json"
            },
            body: input,
            // Добавляем timeout для предотвращения зависания
            signal: AbortSignal.timeout(60000) // 60 секунд для генерации документа
        });

        console.log("📡 Статус ответа:", res.status);

        if (!res.ok) {
            console.error(`❌ HTTP ошибка: ${res.status} ${res.statusText}`);
            return {
                success: false,
                message: `Ошибка сервера: ${res.status} ${res.statusText}`,
                files: {
                    xlsx: {
                        url: "#",
                        fileName: "error.xlsx"
                    },
                    pdf: null
                }
            };
        }

        const data = (await res.json()) as OfferResponse;
        console.log("🚀 ~ generate xlsx doc ~ data:", data);

        // Валидация ответа
        if (!data || typeof data.success !== 'boolean') {
            console.error("❌ Некорректная структура ответа от API");
            return {
                success: false,
                message: "Получен некорректный ответ от сервера",
                files: {
                    xlsx: {
                        url: "#",
                        fileName: "error.xlsx"
                    },
                    pdf: null
                }
            };
        }

        // Проверяем, что файлы действительно созданы
        if (data.success && (!data.files?.xlsx?.url || data.files.xlsx.url === "#")) {
            console.error("❌ Сервер вернул success=true, но файлы не созданы");
            return {
                success: false,
                message: "Документ не был создан на сервере",
                files: {
                    xlsx: {
                        url: "#",
                        fileName: "error.xlsx"
                    },
                    pdf: null
                }
            };
        }

        return data;
    } catch (error) {
        console.error("❌ Ошибка при запросе к API:", error);
        return {
            success: false,
            message: `Ошибка при создании документа: ${error instanceof Error ? error.message : 'Неизвестная ошибка'}`,
            files: {
                xlsx: {
                    url: "#",
                    fileName: "error.xlsx"
                },
                pdf: null
            }
        };
    }
}
