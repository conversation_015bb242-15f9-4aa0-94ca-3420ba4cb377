<script setup lang="ts">
import { useUserStore } from '@/stores/user'
import Avatar from 'primevue/avatar'
import Button from 'primevue/button'
import ConfirmPopup from 'primevue/confirmpopup'
import Menubar from 'primevue/menubar'
import { useConfirm } from 'primevue/useconfirm'
import { useRouter } from 'vue-router'
// import ThemeSwitcher from '@/components/ui/ThemeSwitcher.vue'

const { getRoutes, push } = useRouter()

//console.log('getRoutes:', getRoutes())

const { userData } = useUserStore()

const confirm = useConfirm()

const routerMenuItems = getRoutes()
  .filter((item) => item.meta.isMenuItem)
  .sort((a, b) => a.meta.sort - b.meta.sort)
  .map((item) => ({
    label: item.name,
    to: item.path,
    isSU: item.meta.isSU,
    route: item.path
    // command: () => push(item.path)
    // icon: item.meta.icon
  }))
  .filter((item) => (userData.value.user_role != 'su' ? !item.isSU : true))

//console.log('🚀 ~ file: Navbar.vue:19 ~ routerMenuItems:', routerMenuItems)
function logout() {
  window.localStorage.removeItem('token')
  window.location.reload()
}

function openPopup(event) {
  //console.log('🚀 ~ file: Navbar.vue:33 ~ openPopup ~ event:', event)
  confirm.require({
    target: event.currentTarget,
    message: 'Выйти?',
    icon: 'pi pi-exclamation-triangle',
    accept: () => {
      logout()
    },
    reject: () => {}
  })
}
</script>

<template>
  <ConfirmPopup></ConfirmPopup>
  <Menubar :model="routerMenuItems">
    <template #item="{ item, props, hasSubmenu }">
      <router-link v-if="item.route" v-slot="{ href, navigate }" :to="item.route" custom>
        <a v-ripple :href="href" v-bind="props.action" @click="navigate">
          <span :class="item.icon" />
          <span class="ml-2 font-bold">{{ item.label }}</span>
        </a>
      </router-link>
      <a v-else v-ripple :href="item.url" :target="item.target" v-bind="props.action">
        <span :class="item.icon" />
        <span class="ml-2">{{ item.label }}</span>
        <span v-if="hasSubmenu" class="pi pi-fw pi-angle-down ml-2" />
      </a>
    </template>
    <template #start>
      <!-- <img alt="logo" src="/images/logo.svg" height="40" class="mr-2" /> -->
      <!-- <div class="mr-5">МС</div> -->
    </template>
    <template #end>
      <div class="flex items-center space-x-5">
        <ThemeSwitcher/>
        <a class="font-bold" target="_blank" href="https://mirsalnikov.ru"> На сайт <i class="pi pi-external-link"></i></a>
        <Button @click="openPopup($event)" icon="pi pi-user" class="capitalize font-bold" v-if="userData?.user_id" :label="String(userData.user_name)" size="small" />
      </div>
    </template>
  </Menubar>
</template>
