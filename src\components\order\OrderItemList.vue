<script setup lang="ts">
import Button from 'primevue/button'
import InputNumber from 'primevue/inputnumber'
import Listbox from 'primevue/listbox'
import OrderItem from './OrderItem.vue'
import Chip from 'primevue/chip'
import Chips from 'primevue/chips'
import Badge from 'primevue/badge'

defineProps<{
  orderItems: never[]
  npop: any
  npopToggle: (event: any) => void
  prodpopToggle: (event: any, product: any) => void
  orderItemChangeHandler: (event: any, value: any, orderItem: any) => void
  deleteOrderItem: (event: any, orderItem: any) => void
  isWholesalePrices?: boolean
  label?: string
}>()
</script>

<template>
  <div>
    <div class="flex justify-between items-center p-2">
      <span class="block-title">
        <span>{{ label || 'Состав заказа ' }} </span>
        <span class="text-gray-700 dark:text-zinc-300">({{ orderItems.length }})</span>
      </span>
      <Button @click="npopToggle" :icon="'pi ' + (npop.visible ? ' pi-times' : ' pi-plus')"
        class="p-button-sm p-button-rounded p-button-info" />
    </div>

    <div class="mt-2" v-if="orderItems.length">
      <Listbox class="border-0 bg-white dark:bg-zinc-800 shadow-lg" :options="orderItems" :filter="true"
        optionLabel="prod_analogsku" listStyle="max-height:60vh" filterPlaceholder="Поиск по OEM">
        <template #header="slotProps"> </template>
        <template #option="slotProps">
          <div
            :class="slotProps.option.isNew ? 'bg-green-50 dark:bg-transparent dark:border-l-2 dark:border-green-700 dark:rounded-none p-2 rounded-md -m-2' : ''">
            <!-- <img :src="`https://mirsalnikov.ru/data/rti/${slotProps.option.prod_analogsku}.jpg`" width="40" class="mr-2" /> -->
            <div>
              <div class="flex justify-between space-x-2 items-center">
                <div class="flex flex-wrap items-center space-x-3">
                  <Button @click="deleteOrderItem($event, slotProps.option)" unstyled
                    class="text-red-400 hover:text-red-600" text rounded icon="pi pi-times" />

                  <div>
                    {{ slotProps.option.prod_analogsku }}
                  </div>

                  ({{ slotProps.option.prod_sku }})
                  <span class="bg-yellow-100 dark:bg-orange-700 dark:font-semibold p-1 rounded-lg mx-2">{{
                    slotProps.option.prod_manuf }}</span>
                  <b>{{ slotProps.option.prod_size }}</b>
                  <span>{{ isWholesalePrices ? slotProps.option.whosaleprice || slotProps.option.prod_discount :
                    slotProps.option.prod_price }} руб.шт.</span>
                  <Button unstyled @click="(e) => prodpopToggle(e, slotProps.option)" icon="pi pi-eye" size="large"
                    text></Button>
                  <a target="_blank" :href="'/products/' + slotProps.option.prod_id"
                    class="text-slate-500 dark:text-gray-300">
                    <Button unstyled icon="pi pi-external-link" size="large" text></Button>
                  </a>
                </div>
                <div>
                  <OrderItem :product="slotProps.option" :orderItemChangeHandler="orderItemChangeHandler" />
                </div>
              </div>
            </div>
          </div>
          <div v-if="slotProps.option.specs" class="flex justify-start items-center gap-3 p-2 rounded-md bg-slate-50">
            <div class="text-slate-800 font-semibold">Спецификации:</div>
            <div class="p-2 flex flex-col items-center py-1 shadow-sm bg-yellow-50" v-for="(spec, index) in slotProps.option.specs || []" :key="spec.spec_id">
              <div>
                <Badge v-tooltip.top="spec.spec_title" severity="secondary" :value="spec.spec_id"> </Badge>
              </div>
              <div><span v-if="spec.np">№{{ spec.np }} </span> | кол-во: {{ spec.qty }}</div>
            </div>
          </div>
        </template>
      </Listbox>
    </div>
  </div>
</template>
