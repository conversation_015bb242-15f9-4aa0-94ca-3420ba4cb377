<template>
  <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2">
    <div class="bg-white dark:bg-zinc-900 rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-base leading-6 font-semibold text-slate-600 dark:text-zinc-100 mb-4">Топ продаж ({{ new Date().getFullYear() }})</h3>
        <ScrollPanel style="width: 100%; height: 275px" class="custom">
          <ul>
            <li v-for="(item, index) in topProducts.topSales" :key="index" class="border-t border-gray-200 px-4 py-4 sm:px-6">
              <router-link :to="'/goods?limit=20&searchvalue=' + item.prod_sku" class="flex hover:underline items-center justify-between">
                <div class="text-sm font-medium text-slate-600 dark:text-zinc-100">{{ item.prod_sku }}</div>
                <div class="text-sm font-medium text-gray-700">{{ item.total_sales }}</div>
              </router-link>
            </li>
          </ul>
        </ScrollPanel>
      </div>
    </div>

    <div class="bg-white dark:bg-zinc-900 rounded-lg">
      <div class="px-4 py-5 sm:p-6">
        <h3 class="text-base leading-6 font-semibold text-slate-600 dark:text-zinc-100 mb-4">Топ запросов (Весь)</h3>
        <ScrollPanel style="width: 100%; height: 275px" class="custom">
          <ul>
            <li v-for="(item, index) in topProducts.topRequest" :key="index" class="border-t border-gray-200 px-4 py-4 sm:px-6">
              <router-link :to="'/goods?limit=20&searchvalue=' + item.query" class="flex hover:underline items-center justify-between">
                <div class="text-sm font-medium text-slate-600 dark:text-zinc-100">{{ item.query }}</div>
                <div class="text-sm font-medium text-gray-700">{{ item.total_count }}</div>
              </router-link>
            </li>
          </ul>
        </ScrollPanel>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import ScrollPanel from 'primevue/scrollpanel'
import { defineProps } from 'vue'

interface TopSale {
  prod_id: number
  prod_sku: string
  prod_analogsku: string
  prod_cat: string
  prod_count: number
  total_sales: number
}

interface TopRequest {
  query: string
  total_count: number
}

interface TopProducts {
  topSales: TopSale[]
  topRequest: TopRequest[]
}

const props = defineProps({
  topProducts: {
    type: Object as () => TopProducts,
    required: true
  }
})
</script>

<style>
/* Tailwind styles */
</style>
