<script setup lang="ts">
import { onBeforeMount, onMounted, ref, watch } from 'vue'
import Inplace from 'primevue/inplace'
import { trnColumns } from '../../lib/trnColumns'

import Textarea from 'primevue/textarea'

import But<PERSON> from 'primevue/button'

import type { Client } from '@/lib/interfaces/Order'

const props = defineProps({
  client: {
    type: Object,
    required: true
  },
  mode: {
    type: String,
    required: false
  }
})

const client = ref(props.client)

watch(props.client, () => console.log('watch client prop: ', props.client))

const clientFields = ref([
  'client_country',
  'client_city',
  'client_street',
  'client_house',
  'client_flat',
  'client_postindex',
  // 'client_id',
  'client_mail',
  'client_name',
  // 'client_number',loadCounters
  'client_phone',
  'client_discount',
  'client_cdekid'
])

const orgFields = ['org_name', 'org_adress', 'org_inn', 'org_kpp', 'org_bank', 'org_bik', 'org_kschet', 'org_rschet', 'org_vat']

const orgIcons = {
  'org_name': 'pi-briefcase',
  'org_adress': 'pi-map-marker',
  'org_inn': 'pi-file',
  'org_kpp': 'pi-file',
  'org_bank': 'pi-building',
  'org_bik': 'pi-building',
  'org_rschet': 'pi-credit-card',
  'org_kschet': 'pi-credit-card',
  'org_vat': 'pi-info'
}

const fieldIcons = {
  client_city: 'pi-map-marker',
  client_phone: 'pi-phone',
  'client_country': 'pi-flag',
  'client_flat': 'pi-info',
  'client_house': 'pi-info',
  // 'client_id': '',
  'client_mail': 'pi-inbox',
  'client_name': 'pi-user',
  'client_number': 'pi-user',
  'client_postindex': 'pi-map',
  'client_street': 'pi-map-marker',
  'client_discount': 'pi pi-percentage',
  'client_cdekid': 'pi pi-book'
}

// const emit = defineEmits(['delete-org'])

// const deleteOrg = () => {
//   // emit('delete-org')
//   props.org = {}
// }

onMounted(() => {
  //console.log('🚀 ~ file: ClientData.vue:77 ~ onMounted ~ props:', props)
  if (props.client) {
    !client.value.org && (client.value.org = props.client.client_company)
    // //console.log('props.client: ', props.client)

    if (props.mode == 'order') {
      clientFields.value = clientFields.value.filter((i) => i != 'client_discount')
    }
  }
})
</script>

<template>
  <div v-if="client">
    <div>
      <Button @click="() => delete client.org" text v-if="client.org" label="Удалить организацию" icon="pi pi-minus" class="float-right p-button-sm p-button-secondary" />
      <Button @click="() => (client.org = {})" text v-else label="Добавить организацию" icon="pi pi-plus" class="float-right p-button-sm p-button-secondary" />
    </div>
    <div v-for="(field, index) in clientFields" :key="index">
      <div class="mt-3 flex items-center space-x-2">
        <i :class="'pi ' + fieldIcons[field]" />
        <Inplace :closable="true">
          <template #display>
            <span class="text-gray-500 dark:text-gray-200">{{ trnColumns(field) }}:</span> <span class="text-gray-500 dark:text-gray-200 font-bold">{{ client[field] || 'Редактировать' }}</span>
          </template>
          <template #content>
            <Textarea v-model="client[field]" autoResize autoFocus />
          </template>
        </Inplace>
      </div>
    </div>
  </div>

  <div v-if="client.org" class="bg-white dark:bg-zinc-900 p-4 rounded-md">
    <div v-for="(field, index) in orgFields" :key="index">
      <div class="mt-3 flex items-center space-x-2">
        <i :class="'pi ' + orgIcons[field]" />
        <Inplace :closable="true">
          <template #display>
            <span class="text-gray-500 dark:text-gray-200">{{ trnColumns(field) }}:</span> <span class="text-gray-500 dark:text-gray-200 font-bold">{{ client.org[field] || 'Редактировать' }}</span>
          </template>
          <template #content>
            <Textarea v-model="client.org[field]" autoResize autoFocus />
          </template>
        </Inplace>
      </div>
    </div>
  </div>
</template>
