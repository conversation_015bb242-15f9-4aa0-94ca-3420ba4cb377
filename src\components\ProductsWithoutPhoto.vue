<script lang="ts" setup>
import { api } from '@/lib/_api'
import { useQuery } from '@tanstack/vue-query'
import ScrollPanel from 'primevue/scrollpanel'

const { isFetching, isSuccess, isError, data, error, refetch } = useQuery({
  queryKey: ['productsWithoutPhoto'],
  queryFn: async () => await loadData(),
  refetchOnWindowFocus: false,
  refetchOnMount: true
})

async function loadData() {
  const res = await api('/service/products/productwithoutphoto')
  return res.body
}
</script>

<template>
  <span class="text-slate-600 dark:text-zinc-100 font-semibold">Товары без фото</span>

  <ScrollPanel style="height: 320px" class="custom mt-3">
    <ul>
      <li :key="item.id" v-for="(item, index) in data?.data">
        <a class="hover:underline" :href="'/goods?searchvalue=' + item.oem" target="_blank">
          <span class="font-semibold">{{ index + 1 }}.</span> <span> {{ item.oem }} / {{ item.code }}</span>
        </a>
      </li>
    </ul>
  </ScrollPanel>
</template>
