<script setup lang="ts">
import { editor, view } from '@overlapmedia/imagemapper'
import { onMounted, ref, watch } from 'vue'
import { useConfirm } from 'primevue/useconfirm'
import ConfirmPopup from 'primevue/confirmpopup'
import ConfirmDialog from 'primevue/confirmdialog'
import FindProduct from '@/components/product/FindProduct.vue'
import FileUpload from 'primevue/fileupload'
import Dropdown from 'primevue/dropdown'
import InputText from 'primevue/inputtext'
import Button from 'primevue/button'
import InputSwitch from 'primevue/inputswitch'

interface SchemaItem {
  id: string
  productId: number
  title: string
  type: string
  related?: boolean | undefined
  data: {
    x: number
    y: number
    width: number
    height: number
  }
}

interface InitData {
  img: string
  schema: {
    components: SchemaItem[]
    idCounter: string
  }
}

const props = defineProps({
  product: {},
  initData: {}
})

const emit = defineEmits(['success', 'delete'])

const drawComponents = ref<Array<SchemaItem>>([])
const Editor = ref()
const View = ref()
const drawMode = ref('rect')
const mounted = ref(false)

const base64Image = ref('')
const base64ImageRumi = ref('')

const confirm = useConfirm()

const currentComponentTitle = ref('')
const currentComponentProduct = ref()

const initData = ref<InitData>(props.initData as InitData)

watch(drawMode, () => {
  if (mounted.value) {
    Editor.value[drawMode.value]()
  }
})

watch(drawComponents, () => console.log('drawComponents:', drawComponents.value), { deep: true })

const initEditor = (img) => {
  Editor.value = editor('map-editor', {
    // width: '100%',
    // height: '100%',
    selectModeHandler: () => console.log('Editor is now in select mode'),
    componentDrawnHandler: (component, componentId) => {
      // component.freeze();

      confirm.require({
        target: component.editorOwner.svg,
        // target: component.element.parentNode,
        group: 'drawpopup',
        message: 'Наименование',
        icon: 'pi pi-question-circle',
        acceptIcon: 'pi pi-check',
        acceptClass: 'px-3',
        acceptLabel: 'Создать',
        accept: () => {
          const newComponent = {
            id: componentId + String(Math.random()),
            productId: currentComponentProduct?.value?.prod_id || '',
            // related:
            title: currentComponentTitle?.value || '',
            type: component.element.nodeName,
            data: component.points ? [...component.points] : { ...component.dim }
          }

          // drawComponents.value.push(newComponent)
          drawComponents.value = [...drawComponents.value, newComponent]

          currentComponentProduct.value = {}
          currentComponentTitle.value = ''

          View.value?.import(
            JSON.stringify({
              idCounter: 1,
              components: drawComponents.value
            })
          )

          drawComponents.value.map((comp) => {
            let svg = View?.value.getComponentById(comp.id)
            svg.element.innerHTML = `<title class="svg-title">${comp.title}</title>`
          })

        }
      })
    }
  })

  window.document.querySelector('#map-editor image')?.remove()
  Editor.value.loadImage('', 700, 500)
  window.document.querySelector('#map-editor image').setAttribute('href', String(base64Image.value))
  // window.document.querySelector('#map-editor image').setAttribute('width', 700)
  // window.document.querySelector('#map-editor image').setAttribute('height', 500)

  Editor.value[drawMode.value]()
}

const initView = (img) => {
  View.value = view('map-view', {
    width: 600,
    height: 600,
    viewClickHandler: (e, id) => {
      console.log(
        'find: ',
        drawComponents.value.find((x) => x.id == id)
      )
    }
  })

  window.document.querySelector('#map-view image')?.remove()
  View.value?.loadImage('', 700, 500)
  window.document.querySelector('#map-view image').setAttribute('href', String(base64Image.value))

  // window.document.querySelector('#map-view image').setAttribute('width', 700)
  // window.document.querySelector('#map-view image').setAttribute('height', 500)

  // myView.import(
  //   '{"idCounter":4,"components":[{"id":"rect_1","type":"rect","data":{"x":66,"y":36,"width":253,"height":148}},{"id":"polygon_2","type":"polygon","data":[{"x":376,"y":172},{"x":498,"y":291},{"x":625,"y":174},{"x":500,"y":57}]},{"id":"polygon_3","type":"polygon","data":[{"x":54,"y":249},{"x":234,"y":246},{"x":236,"y":225},{"x":415,"y":270},{"x":237,"y":313},{"x":235,"y":294},{"x":54,"y":292}]}]}'
  // );
}

function useCurrentComponentProduct() {
  currentComponentProduct.value = props.product
  currentComponentTitle.value = `${currentComponentProduct.value.prod_purpose} ${currentComponentProduct.value.prod_sku}`
}

function selectComponentProduct(product) {
  // //console.log('selectComponentProduct:', product)

  currentComponentProduct.value = product
  currentComponentTitle.value = `${product.prod_purpose} ${product.prod_sku}`
}

async function applyWatermark(image, watermarkURL) {
  // create a canvas and draw the image on it
  let canvas = document.createElement('canvas')
  // canvas.width = image.width;
  // canvas.height = image.height;
  canvas.width = 700 // specify the desired width
  canvas.height = 500 // specify the desired height

  let ctx = canvas.getContext('2d')
  ctx.drawImage(image, 0, 0, canvas.width, canvas.height)

  // fetch the watermark data
  const response = await fetch(watermarkURL)
  const blob = await response.blob()

  // create an image object from the fetched data
  let watermark = new Image()
  watermark.src = URL.createObjectURL(blob)

  // wait for the watermark to load
  await new Promise((resolve) => (watermark.onload = resolve))

  // draw the watermark on the canvas
  let watermarkAspectRatio = watermark.width / watermark.height
  let canvasAspectRatio = canvas.width / canvas.height
  let scale = 1
  if (watermarkAspectRatio > canvasAspectRatio) {
    scale = canvas.width / watermark.width
  } else {
    scale = canvas.height / watermark.height
  }

  // resize the watermark to fit on the canvas
  let watermarkWidth = watermark.width * scale
  let watermarkHeight = watermark.height * scale

  // calculate the position of the watermark on the canvas
  let x = (canvas.width - watermarkWidth) / 2
  let y = (canvas.height - watermarkHeight) / 2

  // draw the watermark on the canvas
  // let ctx = canvas.getContext("2d");
  ctx.drawImage(watermark, x, y, watermarkWidth, watermarkHeight)

  // return the base64 encoded string from the canvas
  return canvas.toDataURL('image/jpeg', 1)
}

async function uploadShemaImage(event) {
  const reader = new FileReader()
  const file = event.files[0]

  reader.onloadend = async () => {
    base64Image.value = reader.result

    // create an image object from the file data
    let image = new Image()
    image.src = reader.result

    // wait for the image to load
    await new Promise((resolve) => (image.onload = resolve))

    // fetch the watermark data
    let watermarkedImage = await applyWatermark(image, '/data/watermark/rti.png') //canvas.toDataURL("image/jpeg", 0.6)

    base64Image.value = watermarkedImage
    base64ImageRumi.value = await applyWatermark(image, '/data/watermark/rumi.png')

    // //console.log('base64ImageRumi.value:', base64ImageRumi.value);

    initEditor(watermarkedImage)
    initView(watermarkedImage)
  }

  reader.readAsDataURL(file)
}

function successHandler() {
  //console.log('succ handler')
  emit('success', {
    img: String(base64Image.value),
    img_rumi: String(base64ImageRumi.value),
    schema: {
      idCounter: '1',
      components: drawComponents.value
    }
  })
}

function deleteHandler() {
  emit('delete')
}

function removeSchemaItem(id: string) {
  drawComponents.value = drawComponents.value.filter((x) => x.id !== id)
  Editor.value?.removeComponent(id)
  View.value?.removeComponent(id)

  // View?.value.import(
  //   JSON.stringify({
  //     idCounter: 1,
  //     components: drawComponents.value,
  //   })
  // );
}

onMounted(() => {
  // //console.log('initData:', initData.value);
  // //console.log('props:', props)

  if (initData.value?.img) {
    const components = initData.value.schema.components.map((_item) => {
      let item = { ..._item }

      if (typeof item.related === 'undefined') {
        item.related = false

        Object.defineProperty(item, 'related', {
          value: false,
          writable: true
        })
      }

      return item
    })

    drawComponents.value = components //initData.value.schema.components

    initEditor('')
    initView('')

    //initData.value.img

    window.document.querySelector('#map-view image').setAttribute('href', initData.value.img)
    window.document.querySelector('#map-editor image').setAttribute('href', initData.value.img)

    base64Image.value = initData.value.img
    base64ImageRumi.value = initData.value.img_rumi

    View.value?.import(JSON.stringify(initData.value.schema))
    Editor.value?.import(JSON.stringify(initData.value.schema))

    initData.value?.schema?.components?.map((comp) => {
      let viewSvg = View.value.getComponentById(comp.id)
      viewSvg.element.innerHTML = `<title class="svg-title">${comp.title}</title>`

      let editorSvg = View.value.getComponentById(comp.id)
      editorSvg.element.innerHTML = `<title class="svg-title">${comp.title}</title>`
    })
  }

  mounted.value = true
})
</script>

<template>
  <div class="flex justify-between">
    <div class="flex space-x-5">
      <Dropdown v-show="base64Image" v-model="drawMode" :options="['rect', 'polygon', 'circle', 'ellipse']" placeholder="Метод" />
      <FileUpload mode="basic" name="schema_image[]" accept="image/*" :maxFileSize="1000000" :customUpload="true" @uploader="uploadShemaImage" :auto="true" chooseLabel="Загрузить изображение" />
    </div>
    <div>
      <Button text v-show="base64Image" class="p-button-success" icon="pi pi-save" label="Сохранить" @click="successHandler" />
    </div>
    <div>
      <Button text severity="danger" v-show="base64Image" class="p-button-danger" icon="pi pi-times" label="Удалить схему" @click="deleteHandler" />
    </div>
  </div>
  <div class="mt-5 grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-5">
    <div>
      <span>Метки</span>
      <div class="border">
        <svg style="width: 700px; height: 500px" class="map-editor" id="map-editor"></svg>
      </div>
      <div>
        <div
          class="mt-3 bg-gray-100 hover:bg-yellow-100 dark:bg-gray-700 dark:hover:bg-gray-800 cursor-pointer rounded-md px-3 p-1 shadow text-gray-600 dark:text-gray-200"
          :key="key"
          v-for="(item, key) in drawComponents"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <span class="text-slate-500 dark:text-gray-300 font-bold">{{ key + 1 }}. </span> <span v-show="item.productId"> (id: {{ item.productId }}) - </span> <b>{{ item.title }}</b>
            </div>
            <Button size="sm" text severity="danger" @click="removeSchemaItem(item.id)" class="p-button-sm p-button-rounded p-button-text text-red-400" icon="pi pi-times" />
          </div>
          <div class="flex justify-end">
            <div class="flex fflex-col space-x-2 items-center">
              <div class="text-slate-500 dark:text-gray-300">Добавить текущую схему в этот товар</div>
              <InputSwitch v-model="item.related" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div>
      <span>Предпросмотр</span>
      <div class="border">
        <svg style="width: 700px; height: 500px" class="map-view" id="map-view"></svg>
      </div>
    </div>
  </div>

  <ConfirmDialog class="w-6/12" group="drawpopup">
    <template #message="slotProps">
      <div class="p-3 w-full">
        <div class="flex p-4 w-full justify-center">
          <i :class="slotProps.message.icon" style="font-size: 1.5rem"></i>
          <p class="pl-2">{{ slotProps.message.message }}</p>
        </div>
        <div class="space-y-3 flex-col text-center items-center justify-center">
          <InputText class="w-full" v-model="currentComponentTitle" placeholder="Наименование точки" />
        </div>
        <div class="flex justify-between space-x-5 items-center mt-10">
          <div class="mt-0">
            <div class="font-semibold">Найти</div>
            <FindProduct previewProductTarget=".previewProductCard" :deepSwitch="true" class="w-full" @select="selectComponentProduct"></FindProduct>
          </div>
          <Divider layout="vertical"><span class="font-bold">ИЛИ</span></Divider>
          <div class="flex justify-end">
            <Button v-if="props.product?.prod_id" @click="useCurrentComponentProduct" icon="pi pi-check" text class="p-button-sm p-button-secondary mt-4" label="Использовать текущий товар" />
          </div>
        </div>
      </div>
    </template>
  </ConfirmDialog>
</template>

<style>
.map-view rect {
  fill: red;
}

.map-view circle {
  fill: red;
}

.map-view polygon {
  fill: red;
}

.map-view rectangle {
  fill: red;
}

.map-view ellipse {
  fill: red;
}

.svg-title {
  background-color: brown;
}
</style>
