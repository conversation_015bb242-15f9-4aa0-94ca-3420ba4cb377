<template>
  <div class="chat-message-content">
    <!-- Отображение Tool Calls -->
    <div
      v-if="message.toolCalls && message.toolCalls.length > 0"
      class="mb-3 space-y-2"
    >
      <div
        v-for="toolCall in message.toolCalls"
        :key="toolCall.displayId"
        class="p-3 border rounded-lg"
        :class="{
          'bg-yellow-50 border-yellow-200': toolCall.input !== undefined,
          'bg-green-50 border-green-200': toolCall.output !== undefined
        }"
      >
        <!-- Блок для вызова (параметры) -->
        <div v-if="toolCall.input !== undefined">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="pi pi-wrench text-blue-500 mr-2"></i>
                    <span class="font-medium text-sm">{{ formatToolName(toolCall.toolName) }}</span>
                </div>
                <span v-if="toolCall.status === 'executing'" class="ml-auto text-xs text-gray-500">
                    <i class="pi pi-spin pi-spinner"></i>
                </span>
            </div>
            <details class="text-xs mt-1">
                <summary class="cursor-pointer text-gray-600 hover:text-gray-800">
                    Параметры
                </summary>
                <pre class="mt-1 bg-white p-2 rounded text-xs overflow-x-auto">{{ JSON.stringify(toolCall.input, null, 2) }}</pre>
            </details>
        </div>

        <!-- Блок для результата -->
        <div v-if="toolCall.output !== undefined">
            <div class="flex items-center">
                <i class="pi pi-check-circle text-green-500 mr-2"></i>
                <span class="font-medium text-sm">{{ formatToolName(toolCall.toolName) }} - Результат</span>
            </div>
            <details class="text-xs mt-1">
                <summary class="cursor-pointer text-gray-600 hover:text-gray-800">
                    Показать результат
                </summary>
                <pre class="mt-1 bg-white p-2 rounded text-xs overflow-x-auto">{{ JSON.stringify(toolCall.output, null, 2) }}</pre>
            </details>
        </div>
      </div>
    </div>

    <MarkdownRenderer
      v-if="message.content"
      :content="message.content"
      :open-links-in-new-tab="true"
    />
  </div>
</template>

<script setup lang="ts">
import MarkdownRenderer from '@/components/common/MarkdownRenderer.vue'
import type { Message } from '@/lib/interfaces/Agent'

interface Props {
  message: Message
}
defineProps<Props>()

const formatToolName = (toolName: string): string => {
  const toolNames: Record<string, string> = {
    searchTool: 'Поиск товаров',
    findOrgTool: 'Поиск реквизитов организации',
    generateDocTool: 'Генерация документов',
  }
  return toolNames[toolName] || toolName
}
</script>

<style scoped>
.chat-message-content {
  @apply text-sm;
}
</style>
