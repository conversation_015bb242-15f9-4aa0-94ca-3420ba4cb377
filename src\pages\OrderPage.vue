<script setup lang="ts">
import { defineProps, onMounted, onUnmounted, ref, watch } from 'vue'
import { api } from '@/lib/_api'

import Skeleton from 'primevue/skeleton'
import OrderView from '@/components/order/OrderView.vue'
import { useRoute, useRouter } from 'vue-router'
import ProgressSpinner from 'primevue/progressspinner'
import Message from 'primevue/message'

const order = ref({})
const loading = ref(true)
const error = ref()

const route = useRoute()
const router = useRouter()
let rTM: unknown = undefined

const loadOrder = async () => {
  loading.value = true
  const res = await api('/cpan/order/' + route.params.id)
  if (res.ok) {
    order.value = res.body
  } else {
    error.value = res.status == 404 ? 'Заказ не найден' : res.statusText + ' ' + res.message
    rTM = setTimeout(() => {
      router.push('/orders')
    }, 3000)
  }

  loading.value = false
}

// watch(order, () => //console.log('watcher order: ', order.value))

function saveChanges() {
  delete order.value?.data?.order_id
  loadOrder()
}

onMounted(async () => {
  await loadOrder()

  try {
    document.title = 'Заказ #' + route.params.id
  } catch (error) {}
})

onUnmounted(() => {
  rTM && clearTimeout(rTM)
})
</script>

<template>
  <!-- <span class="text-lg font-semibold text-gray-600 dark:text-gray-200">Заказ #{{ order?.data?.order_id }}</span> -->
  <div>
    <div v-if="!loading">
      <OrderView v-if="order?.data?.order_id" @saveChanges="saveChanges" showTitle :order="order" />
    </div>
    <div class="flex flex-col items-center justify-center h-screen" v-else>
      <div class="text-slate-400 font-bold text-lg">Загрузка данных по заказу</div>
      <ProgressSpinner />
    </div>
    <Message :closable="false" v-if="error" severity="error">
      <b>{{ error }}</b>
    </Message>
  </div>
</template>
