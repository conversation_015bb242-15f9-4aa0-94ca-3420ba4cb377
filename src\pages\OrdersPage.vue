<script setup lang="ts">
import { api } from '@/lib/_api'
import { useQuery } from '@tanstack/vue-query'
import Badge from 'primevue/badge'
import Button from 'primevue/button'
import Column from 'primevue/column'
import DataTable from 'primevue/datatable'
import Dropdown from 'primevue/dropdown'
import InputSwitch from 'primevue/inputswitch'
import InputText from 'primevue/inputtext'
import Paginator from 'primevue/paginator'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

const confirm = useConfirm()
const toast = useToast()

let URL = new URLSearchParams(window.location.search)

if (!URL.has('limit')) {
  URL.set('limit', '50')
}

const { isFetching, isError, data, error, refetch } = useQuery({
  queryKey: ['orders'], //[route.fullPath],
  queryFn: loadData,
  onSuccess: onSuccessLoadData,
  refetchOnMount: true,
  refetchOnWindowFocus: false,
  refetchOnReconnect: true,
  retry: 2
})

watch(route, () => {
  //console.log('route fullPath changed:', route.fullPath)

  if (route.fullPath == '/orders') {
    URL = new URLSearchParams()
    globalSearchValue.value = ''
  }

  refetch()
})

const orders = ref()
const loading = ref(true)

const currentPage = ref(1)

const sortField = ref(''),
  sortOrder = ref(''),
  globalSearchValue = ref(),
  deepSearch = ref(''),
  showСanceled = ref(false),
  showFullGtd = ref(false),
  isRumi = ref(false)

const statuses = ref(['Не обработан', 'В ожидании оплаты', 'Оплачен', 'Отменен', 'Отправлен', 'Отправлен без оплаты' , 'Завершен', 'Резерв'])

const totalRecords = ref(0)
const pageSize = ref(50)
const selectedOrders = ref([])
const mergeLoading = ref(false)

const filters = ref({
  'order_status': { value: '', matchMode: 'equals' },
  'order_payment': { value: '', matchMode: 'equals' },
  'order_shipping': { value: '', matchMode: 'equals' },
  'order_locale': { value: '', matchMode: 'equals' }
})

function getQueryParams() {
  //console.log('getQueryParams URL: ', URL.toString())

  let { page = 1, sortField = 'order_id', sortOrder = 'desc', limit = 20, searchvalue = '', filters = '' } = Object.fromEntries(URL.entries())
  let _filters = ''

  if (filters) {
    try {
      _filters = JSON.stringify(JSON.parse(filters))
    } catch (error) {
      console.warn('parse filters: ', error)
    }
  }

  let params = {
    sortOrder: sortOrder,
    sortField,
    page,
    limit,
    searchvalue: searchvalue || globalSearchValue.value || '',
    filters: _filters,
    debug: true,
    reload: String(Math.random())
  }

  return params
}

async function splitDataHandler() {
  let _data = data.value
  orders.value = _data?.orders?.data
  totalRecords.value = _data?.orders?.meta.total
  pageSize.value = _data?.orders?.meta.per_page
  currentPage.value = _data?.orders?.meta.current_page
}

watch(data, () => {
  splitDataHandler()
})

// watch(
//   filters,
//   () => {
//     //console.log('@@filters update', filters);

//     refetch()
//   },
//   {
//     deep: true
//   }
// )

async function mergeOrdersHandler() {
  try {
    const ids = selectedOrders.value?.map?.((i) => i.order_id)

    mergeLoading.value = true
    const res = await api('/cpan/orders/merge' + '?ids=' + ids.join())
    if (res.ok) {
      toast.add({ severity: 'success', summary: 'Заказы объединены', detail: '', life: 3000 })
      router.push('/orders/' + ids[0])
    }
  } catch (error) {
    console.error(error)
    toast.add({ severity: 'error', summary: `Ошибка cервера: `, detail: String(error), life: 6000 })
  }

  selectedOrders.value = []

  mergeLoading.value = false
}

async function onSuccessLoadData(_data) {
  splitDataHandler()

  if (URL.has('filters')) {
    try {
      let _filters = JSON.parse(URL.get('filters') || '')
      Object.keys(_filters).map((key) => (filters.value[key] = _filters[key]))
    } catch (error) {
      console.warn(error)
    }
  }
}

async function loadData() {
  let { filters: _filters, sortField: _sortField, sortOrder: _sortOrder, searchvalue: _searchValue, deepSearch: _deepSearch } = Object.fromEntries(URL.entries())

  if (_filters) {
    try {
      _filters = JSON.parse(_filters)
      Object.keys(_filters).map((key) => (filters.value[key] = _filters[key]))
    } catch (error) {
      console.warn(error)
    }
  }

  if (_sortField) {
    sortField.value = _sortField
    sortOrder.value = _sortOrder == 'asc' ? -1 : 1
  }

  if (_deepSearch) {
    deepSearch.value = _deepSearch
  }

  if (_searchValue) {
    globalSearchValue.value = _searchValue
  }

  //   loading.value = true

  if (!URL.has('page')) URL.set('page', '1')
  if (!URL.has('limit')) URL.set('limit', '50')

  URL.set('limit', globalSearchValue.value ? '20' : '50')

  const res = await api('/cpan/orders/list/' + '?' + URL.toString())

  if (res.ok) {
    return res.body
  } else {
    throw new Error(res.message)
  }

  //   loading.value = false
}

function onPage(event) {
  URL.set('page', event.page + 1)
  URL.set('limit', event.rows)

  router.push({ query: getQueryParams('fromOnPage') })
}

function onSort(event) {
  //console.log('🚀 ~ file: Goods.vue ~ line 193 ~ onSort ~ event', event)
  URL.set('sortField', event.sortField)
  URL.set('sortOrder', event.sortOrder == -1 ? 'asc' : 'desc')

  router.push({ query: getQueryParams('fromOnSort') })
}

function onFilter(event) {
  //console.log('🚀 ~ file: OrdersPage.vue:186 ~ onFilter ~ event:', event)
  let _filters = {}

  Object.keys(filters.value).map((key) => (filters.value[key].value ? (_filters[key] = filters.value[key]) : ''))
  URL.set('filters', JSON.stringify(_filters))
  router.push({ query: getQueryParams('fromOnFilter') })
}

onMounted(async () => {
  // console.warn('Orders onMount:', data)
  splitDataHandler()

  try {
    document.title = 'Заказы'
  } catch (error) {}

  //   await loadLazyData()
})

let delayTimer

// watch(globalSearchValue, () => {
//   clearTimeout(delayTimer)
//   delayTimer = setTimeout(function () {
//     URL.set('searchvalue', globalSearchValue.value)
//     router.push({ query: getQueryParams('fromWatchGlobalSearchValue') })
//   }, 1000)
// })

watch(deepSearch, () => {
  URL.set('deepsearch', String(deepSearch.value))
  if (globalSearchValue.value) {
    searchHandler()
  }
})

watch(showСanceled, () => {
  URL.set('showСanceled', String(showСanceled.value))
  refetch()
})

watch(showFullGtd, () => {
  URL.set('checkFullGtd', String(showFullGtd.value))
  refetch()
})

watch(isRumi, () => {
  if (isRumi.value) {
    URL.set('isRumi', String(isRumi.value))
  } else {
    URL.delete('isRumi')
  }

  router.push({ query: getQueryParams() })
  // refetch()
})

function searchHandler() {
  URL.set('searchvalue', globalSearchValue.value)
  router.push({ query: getQueryParams() })
}

function getColorByStatus(status) {
  const statuses = {
    'Отменен': 'bg-slate-300 dark:bg-slate-700 dark:text-gray-400',
    'Завершен': 'bg-green-300 dark:bg-transparent dark:text-green-600',
    'В ожидании оплаты': 'bg-orange-300 dark:bg-transparent dark:text-orange-600',
    'Оплачен': 'bg-violet-400 dark:bg-transparent dark:text-violet-600',
    'Не обработан': 'bg-rose-300 dark:bg-transparent dark:text-rose-600',
    'Отправлен': 'bg-blue-300 dark:bg-transparent dark:text-blue-600',
    'Резерв': 'bg-pink-300 dark:bg-transparent dark:text-pink-600',
    'Отправлен без оплаты': 'bg-pink-400 dark:bg-transparent dark:text-ping-600'
  }

  return statuses[status]
}

function getColorByPayment(type) {
  const types = {
    'Банковской картой': 'text-emerald-500',
    'По безналичному счету': 'text-blue-500'
  }

  return types[type] || 'text-teal-400'
}

function getColorByShipping(type) {
  const types = {
    'Курьер': 'text-yellow-500',
    'Почта РФ': 'text-sky-500',
    'СДЭК': 'text-green-400',
    'Курьер КСЭ': 'text-orange-500',
    'Курьер KCE': 'text-orange-500'
  }

  return types[type] || 'text-purple-400'
}

function getColorByShop(type) {
  const types = {
    'RTI': 'text-gray-600 dark:text-gray-200',
    'RUMI': 'text-orange-500'
  }

  return types[type] || 'text-purple-400'
}

function wrapFirstWordInBold(str) {
  try {
    const words = str.split(' ')
    words[0] = '<b>' + words[0] + '</b>'
    return words.join(' ')
  } catch (error) {
    return str
  }
}
</script>

<template>
  <h1 class="text-xl font-semibold text-gray-500 dark:text-gray-200">Заказы</h1>

  <div class="bg-white dark:bg-zinc-900 bg shadow-xl shadow-slate-200 dark:shadow-none p-3 rounded-lg text-sm">
    <DataTable
      table-class="table-default"
      :pt="{
        bodyRow: 'p-0',
        column: 'p-0'
      }"
      :unstyled="false"
      size="small"
      :lazy="true"
      :first="Math.ceil((currentPage - 1) * pageSize)"
      :totalRecords="totalRecords"
      :sortField="sortField"
      :sortOrder="sortOrder"
      v-model:filters="filters"
      v-model:selection="selectedOrders"
      dataKey="order_id"
      ref="dt"
      :stripedRows="true"
      _responsiveLayout="stack"
      showGridlines
      :value="orders"
      :paginator="true"
      :rows="pageSize"
      :loading="isFetching"
      @page="onPage($event)"
      @sort="onSort($event)"
      @filter="onFilter($event)"
      filterDisplay="row"
      filterLocale="ru"
      style="font-size: 16px; padding: 0"
      stripedRows
      resizableColumns
      columnResizeMode="fit"
      stateKey="orderstable_1"
      paginatorTemplate="CurrentPageReport FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
      :rowsPerPageOptions="[10, 20, 50, 100, 200, 1000]"
      currentPageReportTemplate="Показаны от {first} до {last} из {totalRecords}"
    >
      <Column selectionMode="multiple" headerStyle="width: 3em"></Column>
      <Column style="width: 40px" :sortable="true" field="order_id" header="ID">
        <template #body="{ data }">
          <router-link :class="'hover:underline font-bold ' + (data.fullGtd ? 'text-green-500' : '')" :to="'/orders/' + data.order_id">{{ data.order_id }}</router-link>
        </template>
      </Column>
      <Column style="width: 60px" :sortable="true" field="order_locale" header="L">
        <template #body="{ data }">
          <div>
            <div
              :class="data.order_locale != 'ru' ? 'text-orange-500' : 'text-gray-600 dark:text-gray-200 dark:text-gray-200 dark:text-gray-200'"
              class="p-1 rounded-md font-semibold text-sm text-center"
            >
              {{ data.order_locale != 'ru' ? 'RUMI' : 'РТИ' }}
            </div>
            <div class="rounded text-xs font-semibold text-white bg-green-600 p-1" severity="success" value="ТАМОЖНЯ" v-if="data.order_gtd">ТАМОЖНЯ</div>
          </div>
        </template>
        <!-- <template #filter="{ filterModel, filterCallback }">
          <Dropdown style="width: 60px" v-model="filterModel.value" :options="['RTI', 'RUMI']" @change="filterCallback()" placeholder="Фильтр">
            <template #option="{ option }">
              <div :class="getColorByShop(option)" class="p-1 rounded-md text-center font-semibold">{{ option }}</div>
            </template>
          </Dropdown>
        </template> -->
      </Column>

      <Column style="width: 190px; text-align: center" :sortable="true" field="order_datetime" header="Дата">
        <template #body="{ data }">
          <router-link class="hover:underline" :to="'/orders/' + data.order_id">{{ data.order_datetime }}</router-link>
        </template>
      </Column>
      <Column style="padding: 0" :showFilterMenu="false" :sortable="true" filterMatchMode="startsWith" field="order_status" header="Статус">
        <template #filter="{ filterModel, filterCallback }">
          <!-- <InputText type="text" v-model="filterModel.value" @keydown.enter="filterCallback()" class="p-column-filter" placeholder="Поиск по"/> -->
          <Dropdown class="text-sm mx-2" v-model="filterModel.value" @change="filterCallback()" :options="statuses" placeholder="Фильтр">
            <template #option="{ option }">
              <div :class="getColorByStatus(option)" class="p-1 -m-2 rounded-md text-xs text-zinc-800 text-center font-semibold">{{ option }}</div>
            </template>
          </Dropdown>
        </template>
        <template #body="{ data, field }">
          <div :class="getColorByStatus(data[field])" class="p-1 mx-2 rounded-md text-zinc-800 ttext-white text-center font-semibold">{{ data[field] }}</div>
        </template>
      </Column>
      <Column style="text-align: center" :sortable="true" field="order_client" header="Получатель">
        <template #body="{ data }">
          <router-link class="dark:text-zinc-300 fioname hover:underline" :to="'/clients/' + data.snapshots?.[0]?.body?.client?.client_id">
            <!-- <span v-html="wrapFirstWordInBold(data.snapshots?.[0]?.body?.client?.client_name)"></span> -->
            <b>{{ data.snapshots?.[0]?.body?.client?.client_name }}</b>
          </router-link>
          <div>
            <b>{{ data.snapshots?.[0]?.body?.client?.client_phone }}</b>
          </div>
        </template>
      </Column>

      <Column body-style="white-space: break-spaces;" field="order_desc" header="Пометки клиента"></Column>
      <Column body-style="white-space: break-spaces;" field="order_notice" header="Примечание">
        <template #body="{ data, field }">
          <div>{{ data[field] }}</div>
          <div v-if="data.snapshots?.[0]?.body?.order_lastupdate_person" class="bg-sky-400 flex items-center text-xs text-white font-bold text-center rounded-md px-1 py-1">
            <i class="pi pi-box text-xs mr-1"></i>
            <span>
              {{ data.snapshots?.[0]?.body?.order_lastupdate_person }}
            </span>
          </div>
        </template>
      </Column>

      <Column style="width: 140px" :sortable="true" field="order_payment" header="Оплата" :showFilterMenu="false">
        <template #filter="{ filterModel, filterCallback }">
          <Dropdown v-model="filterModel.value" :options="['Банковской картой', 'По безналичному счету']" @change="filterCallback()" placeholder="Фильтр">
            <template #option="{ option }">
              <div :class="getColorByPayment(option)" class="p-1 rounded-md text-center font-semibold">{{ option }}</div>
            </template>
          </Dropdown>
        </template>
        <template #body="{ data, field }">
          <div :class="getColorByPayment(data[field])" class="p-1 rounded-md text-center font-semibold">{{ data[field] }}</div>
        </template>
      </Column>
      <Column style="width: 40px" :sortable="true" field="order_shipping" header="Доставка" :showFilterMenu="false">
        <template #filter="{ filterModel, filterCallback }">
          <Dropdown v-model="filterModel.value" :options="['Курьер', 'Почта РФ', 'СДЭК', 'Курьер КСЭ']" @change="filterCallback()" placeholder="Фильтр">
            <template #option="{ option }">
              <div :class="getColorByShipping(option)" class="p-1 rounded-md text-center font-semibold">{{ option }}</div>
            </template>
          </Dropdown>
        </template>
        <template #body="{ data, field }">
          <div :class="getColorByShipping(data[field])" class="p-1 rounded-md text-center font-semibold">{{ data[field] }}</div>
          <div v-if="data.snapshots?.[0]?.body?.client?.client_cdekid" class="text-center text-orange-400 font-bold">CDEK ID</div>
        </template>
      </Column>

      <Column style="width: 140px, text-align: center" :sortable="true" field="order_price" header="Сумма">
        <template #body="slotProps">
          <div class="text-center">
            <span class="font-semibold text-center">{{ (Number(slotProps.data.order_price) + Number(slotProps.data.order_shippingprice)).toLocaleString() }}</span>
          </div>
        </template>
      </Column>
      <template #header>
        <div class="flex justify-between items-center">
          <div class="flex items-center flex-wrap space-x-5">
            <Button text @click="refetch" type="button" icon="pi pi-refresh" label="Обновить" />
            <div class="flex items-center space-x-2">
              <div class="text-sm text-gray-500 dark:text-gray-200 text-right">Полный вывоз</div>
              <InputSwitch v-model="showFullGtd" />
            </div>
            <div class="flex items-center space-x-2">
              <div class="text-sm text-gray-500 dark:text-gray-200 text-right">Отмененные</div>
              <InputSwitch v-model="showСanceled" />
            </div>
            <div class="flex items-center space-x-2">
              <div class="text-sm text-orange-500 text-right font-bold">RUMI</div>
              <InputSwitch v-model="isRumi" />
            </div>
          </div>
          <div class="flex justify-end items-center">
            <div class="mr-4" v-if="selectedOrders?.length > 1">
              <Button :loading="mergeLoading" @click="mergeOrdersHandler" icon="pi pi-check" label="Объединить" class="p-button-secondary" />
            </div>
            <div>
              <InputText @keyup.enter="searchHandler" v-model="globalSearchValue" placeholder="Поиск" />
            </div>
            <Button @click="searchHandler" icon="pi pi-search" />
            <div class="ml-5">
              <div class="text-sm text-gray-500 dark:text-gray-200 text-right">Глубокий</div>
              <InputSwitch v-model="deepSearch" />
            </div>
          </div>
        </div>
        <div>
          <Paginator
            @page="onPage($event)"
            :rows="pageSize"
            :first="Math.ceil((currentPage - 1) * pageSize)"
            :totalRecords="totalRecords"
            template="CurrentPageReport FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
            current-page-report-template="Показаны от {first} до {last} из {totalRecords}"
          />
        </div>
      </template>
      <template #paginatorstart>
        <Button text @click="refetch" type="button" icon="pi pi-refresh" class="p-button-text" />
      </template>
      <template #empty> Нет заказов по заданным параметрам </template>
    </DataTable>
  </div>
</template>

<style></style>
