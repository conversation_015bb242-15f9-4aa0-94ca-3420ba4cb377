import { marked } from 'marked'

// Интерфейсы для структурированного контента (копируем из ChatMessageContent)
interface TextItem {
  type: 'text'
  text: string
}

interface ToolCallItem {
  type: 'tool-call'
  toolCallId: string
  toolName: string
  args: any
}

interface ToolResultItem {
  type: 'tool-result'
  toolCallId: string
  toolName: string
  result: {
    success: boolean
    data?: any
    message?: string
  }
}

type MessageItem = TextItem | ToolCallItem | ToolResultItem

// Настройка marked для email
marked.setOptions({
  gfm: true,
  breaks: true,
  smartypants: true
})

// Универсальный рекурсивный сборщик всех текстовых полей
function extractTextRecursive(input: unknown): string[] {
  const result: string[] = []
  const candidates = ['markdown', 'text', 'message', 'content', 'value', 'body']
  
  if (typeof input === 'string') {
    result.push(input)
  } else if (Array.isArray(input)) {
    for (const item of input) {
      result.push(...extractTextRecursive(item))
    }
  } else if (typeof input === 'object' && input !== null) {
    for (const key of candidates) {
      if (typeof (input as any)[key] === 'string' && (input as any)[key].trim()) {
        result.push((input as any)[key])
      }
    }
    // Рекурсивно по всем полям
    for (const value of Object.values(input)) {
      if (typeof value === 'object' && value !== null) {
        result.push(...extractTextRecursive(value))
      }
    }
  }
  return result
}

// Форматирование названий инструментов для email
const formatToolNameForEmail = (toolName: string): string => {
  const toolNames: Record<string, string> = {
    searchTool: 'Поиск товаров',
    findOrgTool: 'Поиск реквизитов организации',
    generateDocTool: 'Генерация документов',
  }
  return toolNames[toolName] || toolName
}

// Конвертация структурированного контента в HTML для email
function convertStructuredContentToHtml(parsedContent: MessageItem[]): string {
  let html = ''
  
  for (const item of parsedContent) {
    if (item.type === 'text') {
      // Текстовый элемент - конвертируем markdown в HTML
      try {
        html += marked.parse(item.text)
      } catch (error) {
        console.error('Ошибка рендеринга markdown:', error)
        html += `<p>${item.text}</p>`
      }
    } else if (item.type === 'tool-call') {
      // Tool Call элемент - добавляем информацию о вызове инструмента
      html += `
        <div style="background-color: #eff6ff; border: 1px solid #dbeafe; border-radius: 8px; padding: 12px; margin: 8px 0;">
          <div style="display: flex; align-items: center; font-weight: 500; font-size: 14px;">
            🔧 ${formatToolNameForEmail(item.toolName)}
          </div>
        </div>
      `
    } else if (item.type === 'tool-result') {
      // Tool Result элемент - добавляем результат выполнения
      const isSuccess = item.result?.success !== false
      const bgColor = isSuccess ? '#f0fdf4' : '#fef2f2'
      const borderColor = isSuccess ? '#dcfce7' : '#fecaca'
      const textColor = isSuccess ? '#166534' : '#dc2626'
      const icon = isSuccess ? '✅' : '❌'
      
      html += `
        <div style="background-color: ${bgColor}; border: 1px solid ${borderColor}; border-radius: 8px; padding: 12px; margin: 8px 0;">
          <div style="display: flex; align-items: center; font-weight: 500; font-size: 14px; color: ${textColor};">
            ${icon} Результат: ${formatToolNameForEmail(item.toolName)}
          </div>
          ${item.result?.message ? `<div style="margin-top: 8px; font-size: 14px; color: ${textColor};">${item.result.message}</div>` : ''}
        </div>
      `
    }
  }
  
  return html
}

// Основная функция для конвертации контента сообщения в HTML
export function convertMessageContentToHtml(content: string): string {
  if (!content || typeof content !== 'string') {
    return ''
  }

  // 1. Если строка — сначала пробуем как JSON, иначе markdown
  if (typeof content === 'string') {
    // Пробуем распарсить как JSON
    let parsed: unknown = null
    let isJson = false
    try {
      parsed = JSON.parse(content)
      // Только если это массив или объект
      if (typeof parsed === 'object' && parsed !== null) {
        isJson = true
      }
    } catch (e) {
      isJson = false
    }

    if (isJson && Array.isArray(parsed)) {
      // Структурированное сообщение - парсим как MessageItem[]
      try {
        const messageItems: MessageItem[] = parsed.map((item: any): MessageItem => {
          if (item.type === 'text') {
            return {
              type: 'text',
              text: item.text || ''
            }
          } else if (item.type === 'tool-call') {
            return {
              type: 'tool-call',
              toolCallId: item.toolCallId || '',
              toolName: item.toolName || '',
              args: item.args || {}
            }
          } else if (item.type === 'tool-result') {
            return {
              type: 'tool-result',
              toolCallId: item.toolCallId || '',
              toolName: item.toolName || '',
              result: item.result || { success: false }
            }
          } else {
            // Неизвестный тип - возвращаем как есть
            return item as MessageItem
          }
        })
        
        return convertStructuredContentToHtml(messageItems)
      } catch (error) {
        console.error('Ошибка парсинга структурированного контента:', error)
        // Fallback - извлекаем текст рекурсивно
        const texts = extractTextRecursive(parsed)
        if (texts.length > 0) {
          const joined = texts.join('\n\n')
          try {
            return marked.parse(joined)
          } catch (markdownError) {
            console.error('Ошибка рендеринга markdown из extracted:', markdownError)
            return `<p>${joined}</p>`
          }
        }
      }
    } else if (isJson) {
      // JSON объект - извлекаем текст рекурсивно
      const texts = extractTextRecursive(parsed)
      if (texts.length > 0) {
        const joined = texts.join('\n\n')
        try {
          return marked.parse(joined)
        } catch (error) {
          console.error('Ошибка рендеринга markdown из extracted (parsed JSON):', error)
          return `<p>${joined}</p>`
        }
      }
    } else {
      // Не JSON — обычный markdown
      try {
        return marked.parse(content)
      } catch (error) {
        console.error('Ошибка рендеринга markdown:', error)
        return `<p>${content}</p>`
      }
    }
  }

  // Fallback - возвращаем как есть в параграфе
  return `<p>${content}</p>`
}
