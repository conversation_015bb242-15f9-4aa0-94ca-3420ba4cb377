<script setup>
import { computed } from 'vue'
import { useStore } from 'vuex'

const store = useStore()
const isConnected = computed(() => store.getters.getSocketIsConnected)

let successMessage = 'Соединение установлено',
  loadingMessage = 'Подключение к серверу'
</script>

<template>
  <div :class="'status-block ' + (isConnected ? 'text-green-500' : 'text-red-500') + ' flex items-center'" :title="isConnected ? successMessage : loadingMessage">
    <div :class="isConnected ? 'text-green-500' : 'text-red-500' + ' /absolute text-sm font-semibold'">{{ isConnected ? successMessage : loadingMessage }}</div>
    <div class="flex w-10 h-10 items-center justify-center">
      <div v-if="!isConnected" class="blob red"></div>
      <svg v-else class="icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path d="M4 13.4286H11.1429L8.28571 22L19.7143 10.5714H12.5714L15.4286 2L4 13.4286Z" />
      </svg>
    </div>
  </div>
</template>

<style>
.status-block {
  @apply text-sm font-medium
        transition-all ease-in-out duration-200;
}
.icon {
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-miterlimit: 10;
  fill: none;
}
.blob {
  border-radius: 50%;
  height: 8px;
  width: 8px;
  transform: scale(1);
  animation: pulse-black 2s infinite;
}
.blob.red {
  background: rgba(255, 82, 82, 1);
  box-shadow: 0 0 0 0 rgba(255, 82, 82, 1);
  animation: pulse-red 2s infinite;
}
@keyframes pulse-red {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 82, 82, 0.7);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 8px rgba(255, 82, 82, 0);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 82, 82, 0);
  }
}
</style>
