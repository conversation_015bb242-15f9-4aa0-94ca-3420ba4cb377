import { describe, it, expect, vi, beforeEach } from 'vitest'
import { AgentApiClient } from '@/lib/agent/apiClient'

// Мокаем Mastra клиент
const mockSaveMessageToMemory = vi.fn()
const mockMastraClient = {
  saveMessageToMemory: mockSaveMessageToMemory
}

describe('AgentApiClient.saveServiceMessage', () => {
  let apiClient: AgentApiClient

  beforeEach(() => {
    vi.clearAllMocks()
    // Создаем экземпляр клиента и заменяем mastraClient на мок
    apiClient = new AgentApiClient()
    ;(apiClient as any).mastraClient = mockMastraClient
  })

  it('должен передавать threadId и resourceId при сохранении служебного сообщения', async () => {
    const threadId = 'test-thread-123'
    const messageData = {
      type: 'email_sent' as const,
      content: 'Тестовое служебное сообщение',
      metadata: { test: 'data' }
    }

    mockSaveMessageToMemory.mockResolvedValue(undefined)

    await apiClient.saveServiceMessage(threadId, messageData)

    expect(mockSaveMessageToMemory).toHaveBeenCalledTimes(1)
    
    const callArgs = mockSaveMessageToMemory.mock.calls[0][0]
    expect(callArgs).toMatchObject({
      messages: [
        expect.objectContaining({
          role: 'system',
          content: messageData.content,
          threadId: threadId,
          resourceId: 'user-default',
          type: 'text'
        })
      ],
      agentId: 'orgManagerAgent'
    })

    // Проверяем, что сообщение содержит все обязательные поля
    const message = callArgs.messages[0]
    expect(message.threadId).toBe(threadId)
    expect(message.resourceId).toBe('user-default')
    expect(message.id).toMatch(/^service-\d+-/)
    expect(message.createdAt).toBeInstanceOf(Date)
    expect(message.type).toBe('text')
  })

  it('должен обрабатывать ошибки при сохранении', async () => {
    const threadId = 'test-thread-123'
    const messageData = {
      type: 'system_note' as const,
      content: 'Тестовое сообщение',
      metadata: {}
    }

    const error = new Error('Mastra API error')
    mockSaveMessageToMemory.mockRejectedValue(error)

    await expect(apiClient.saveServiceMessage(threadId, messageData)).rejects.toThrow('Mastra API error')
  })
})
