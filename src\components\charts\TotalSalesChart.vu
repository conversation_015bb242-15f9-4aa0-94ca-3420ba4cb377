<script setup>
    import { onMounted, ref } from 'vue'

    const props = defineProps({
        salesCounters: {},
        prev_salesCounters: undefined,
        title: '',
        ordersCounters: []
    })

    //console.log('Total sales PROPS: ', props)

    let options = ref(),
        series = ref([]),
        salesCounters = ref(props.salesCounters),
        prev_salesCounters = ref(props.prev_salesCounters),
        loading = ref(true),
        ordersCounters = ref(props.ordersCounters || []),
        title = ref(props.title);

    const load = () => {
        options.value = {
            chart: {
              id: 'salesChart',
              height: 200,
              type: 'line',
              dropShadow: {
                enabled: true,
                color: '#000',
                top: 18,
                left: 7,
                blur: 10,
                opacity: 0.4
              },
              toolbar: {
                show: false
              }
            },
          colors: ['#718096', '#5fb0ff'],
            dataLabels: {
              enabled: true,
              style: {
                fontSize: "14px",
                fontFamily: "Helvetica, Arial, sans-serif",
                fontWeight: "bold"
              }
            },
            stroke: {
              curve: 'smooth',
              // width: [1, 1, 4]
            },
            title: {
              text: title.value || 'Продажи',
              align: 'left'
            },
            grid: {
              borderColor: '#e7e7e7',
              row: {
                colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
                opacity: 0.5
              },
            },
            markers: {
              size: 2
            },
            xaxis: {
              categories: Object.keys(salesCounters.value),
            },
            legend: {
              fontSize: "18px",
              position: 'top',
              horizontalAlign: 'right',
              floating: true,
              offsetY: -25,
              offsetX: -5
            }
        }

        series.value.push(            {
                name: new Date().getFullYear(),
                type: 'line',
                data: Object.values(salesCounters.value)
        })

      // series.value.push({
      //   name: 'Заказы',
      //   type: 'column',
      //   opposite: true,
      //   axisTicks: {
      //     show: true,
      //   },
      //   data: Object.values(ordersCounters.value)
      // })
    }

    onMounted(() => {


      if (prev_salesCounters.value) {
        series.value.push(           {
              name: (Number(new Date().getFullYear()) - 1),
              type: 'line',
              data: Object.values(prev_salesCounters.value)
        })
      }



      // series.value.reverse()
        load()
        loading.value = false
    })

</script>


<template>
    <div v-if="!loading">
        <apexchart width="100%" height="420px" type="line" :options="options" :series="series"></apexchart>
    </div>
</template>
