<script setup>
    import { onMounted, ref } from 'vue'

    const props = defineProps({
        ordersCounters: {},
        // prev_ordersCounters: {}
        canceledOrdersCounters: {}
    })

    //console.log('canceledOrdersCount orders PROPS: ', props)

    let options = ref(),
        series = ref(),
        ordersCounters = ref(props.ordersCounters),
        // prev_ordersCounters = ref(props.prev_ordersCounters),
        canceledOrdersCounters = ref(props.canceledOrdersCounters),
        loading = ref(true);

    const load = () => {
        options.value = {
            chart: {
              id: 'salesChart',
              height: 350,
              type: 'bar',
              stacked: true,
              stackType: '100%',
              dropShadow: {
                enabled: true,
                color: '#000',
                top: 14,
                left: 5,
                blur: 10,
                opacity: 0.1
              },
              toolbar: {
                show: false
              }
            },
            plotOptions: {
              bar: {
                horizontal: false,
                columnWidth: '80%',
                endingShape: 'rounded'
              },
            },
            colors: ['#4c51bf', '#4a5568'],
            dataLabels: {
              enabled: true,
              style: {
                fontSize: "14px",
                fontFamily: "Helvetica, Arial, sans-serif",
                // fontWeight: "bold"
              }
            },
            stroke: {
              curve: 'smooth',
            },
            title: {
              text: 'Конверсия заказов',
              align: 'left'
            },
            grid: {
              borderColor: '#e7e7e7',
              row: {
                colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
                opacity: 0.5
              },
            },
            markers: {
              size: 2
            },
            xaxis: {
              categories: Object.keys(ordersCounters.value),
            },
            legend: {
              fontSize: "18px",
              position: 'top',
              horizontalAlign: 'right',
              floating: true,
              offsetY: -25,
              offsetX: -5
            }
        }

        series.value = [
            {
                name: 'В работе ',
                data: Object.values(ordersCounters.value) // prev_ordersCounters
            },
            {
                name: 'Отмененные',
                data: Object.values(canceledOrdersCounters.value) // 
            }
        ]
    }

    onMounted(() => {
        load()
        loading.value = false
    })

</script>


<template>
    <div v-if="!loading">
        <apexchart width="100%" type="bar" :options="options" :series="series"></apexchart>
    </div>
</template>
