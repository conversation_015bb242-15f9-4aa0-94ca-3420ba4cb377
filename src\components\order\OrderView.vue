<script setup lang="ts">
import { defineProps, onMounted, ref, watch, computed, onUnmounted, onBeforeUnmount } from 'vue'

import Skeleton from 'primevue/skeleton'
import Listbox from 'primevue/listbox'
import InputNumber from 'primevue/inputnumber'
import { useConfirm } from 'primevue/useconfirm'
import ClientData from '@/components/client/ClientData.vue'
import OrderSpecs from '@/components/order/OrderSpecs.vue'

import Shipping from '@/components/order/Shipping.vue'
import Payment from '@/components/order/Payment.vue'
import Divider from 'primevue/divider'
import MegaMenu from 'primevue/megamenu'
import { api } from '@/lib/_api'
import Caller from '@/components/client/Caller.vue'
import Dropdown from 'primevue/dropdown'
import Toast from 'primevue/toast'
import ScrollPanel from 'primevue/scrollpanel'

import Panel from 'primevue/panel'

//TODO: get from api
import { statusColorClass } from '../../lib/orderStatusColors'
import { orderStatuses } from '../../lib/orderStatuses'
import InputText from 'primevue/inputtext'
import Button from 'primevue/button'

import OverlayPanel from 'primevue/overlaypanel'
import FindProduct from '@/components/product/FindProduct.vue'
import OrderSnapshotsLog from './OrderSnapshotsLog.vue'
import Dialog from 'primevue/dialog'
import { useSettingsStore } from '@/stores/apisettings'
import ProductCard from '@/components/product/ProductCard.vue'
import ProductInfoCard from '@/components/product/ProductInfoCard.vue'
import dayjs from 'dayjs'
import ClientCard from '@/components/client/ClientCard.vue'
import ConfirmPopup from 'primevue/confirmpopup'
import ProgressSpinner from 'primevue/progressspinner'
import OrderItemList from './OrderItemList.vue'
import { toValuteString } from '@/lib/toValuteString'
import { useToast } from 'primevue/usetoast'
import Textarea from 'primevue/textarea'
import OrderDocuments from './OrderDocuments.vue'
import SendMail from '../SendMail.vue'
import ClientMailBox from '../client/ClientMailBox.vue'
import Message from 'primevue/message'
import ClientDebt from '@/components/client/ClientDebt.vue'
import ConfirmDialog from 'primevue/confirmdialog'
import type { StockUpdateError } from '@/lib/interfaces/Order'
import type { Order } from '@/lib/interfaces/Order'
import { useUserStore } from '@/stores/user'
import InputSwitch from 'primevue/inputswitch'
import { trnColumns } from '@/lib/trnColumns'

// @ts-ignore
import QRCode from 'qrcode'

const { userData } = useUserStore()

const { settingsData } = useSettingsStore()

type OrderViewPropsType = {
  order: any
  showTitle: Boolean
}

type OrderSumsType = {
  def: number
  items: number
  total: number
  order: number
  wholesalePrices: boolean
  discounts: {
    personal: number
    big?: number
  }
}

type EmailMeta = {
  to?: string
  subject?: string
  message?: string
}

const confirm = useConfirm()

const order = ref({})
const mounted = ref(false)
const callClientModal = ref(false)
const adnlDiscount = ref()
const npop = ref()
const clientModalShow = ref(false)
const selectedProductModal = ref(false)
const selectedProduct = ref()
const isWholesalePrices = ref()
const currentSnapshot = ref()
const recommendedShippingPrice = ref(0)
const orderSums = ref<OrderSumsType>({} as OrderSumsType)
const sendMailClientModal = ref(false)
const _tempEmailMeta = ref<EmailMeta>({} as EmailMeta)
const saveLoading = ref(false)
const senderIndex = ref(0)
const isPacked = ref(false)
const showClientEditNotice = ref(false)
const orderWeight = ref()
const qrDataUrl = ref('')
const qrUrl = computed(() => `https://api.mirsalnikov.ru/service/qrcodereview?orderId=${order.value?.order_id}&action=follow`)

async function generateOrderQr() {
  try {
    if (order.value?.order_id) {
      // @ts-ignore
      qrDataUrl.value = await QRCode.toDataURL(qrUrl.value, { width: 256, margin: 1 })
    } else {
      qrDataUrl.value = ''
    }
  } catch (e) {
    qrDataUrl.value = ''
  }
}

const stockUpdateErrorsData = ref<StockUpdateError>({ stockUpdateErrors: [] })

const props = defineProps({
  order: { required: true, default: {} },
  showTitle: Boolean
})

const emit = defineEmits(['saveChanges'])
const toast = useToast()

//console.log('pro:', props.order.snapshots[0].body)

const orderItems = ref([])
const debt = ref()

const clientEditOp = ref()
const isGTD = ref(false)

const menuItems = ref([
  {
    label: 'Отменить заказ',
    icon: 'pi pi-ban',
    severity: 'secondary',
    command() {
      confirm.require({
        message: 'Подтверждение отмены заказа',
        icon: 'pi pi-exclamation-triangle',
        accept: () => {
          order.value.order_status = 'Отменен'
          saveChanges()
        },
        reject: () => {}
      })
    }
  },
  {
    label: 'Удалить заказ',
    icon: 'pi pi-times',
    severity: 'danger',
    command() {
      confirm.require({
        message: 'Подтверждение УДАЛЕНИЯ заказа',
        icon: 'pi pi-exclamation-triangle',
        accept: () => {
          order.value.order_status = 'Удален'
          saveChanges()
        },
        reject: () => {}
      })
    }
  },
  {
    label: 'Снять блок',
    icon: 'pi pi-times',
    method: () => leaving('x')
  },
  {
    label: 'Сохранить изменения',
    icon: 'pi pi-save',
    severity: 'success',
    loading: saveLoading,
    command() {
      saveChanges()
    }
  }
])

function getColorByStatus(status) {
  const statuses = {
    'Отменен': 'bg-slate-300 dark:bg-slate-700 dark:text-gray-400',
    'Завершен': 'bg-green-300 dark:bg-transparent dark:text-green-600',
    'В ожидании оплаты': 'bg-orange-300 dark:bg-transparent dark:text-orange-600',
    'Оплачен': 'bg-violet-400 dark:bg-transparent dark:text-violet-600',
    'Не обработан': 'bg-rose-300 dark:bg-transparent dark:text-rose-600',
    'Отправлен': 'bg-blue-300 dark:bg-transparent dark:text-blue-600',
    'Резерв': 'bg-pink-300 dark:bg-transparent dark:text-pink-600',
    'Автооплата': 'bg-orange-400 dark:bg-transparent dark:text-orange-600',
    'Отправлен без оплаты': 'bg-pink-400 dark:bg-transparent dark:text-ping-600'
  }

  return statuses[status]
}

async function leaving(x: any) {
  await api(`/service/lock/?entity=orders&entity_id=${order.value.order_id}&action=${x ? 'forceunlock' : 'unlock'}`)
}

async function checkLock() {
  if (order.value.order_id) {
    const { body: _lock } = await api(`/service/lock/?entity=orders&entity_id=${order.value.order_id}&action=lock`)

    if (_lock?.id && _lock.user_id !== userData.value.user_id) {
      confirm.require({
        message: `Заказ открыт на редактирование: ${_lock.user_name} в ${_lock.created_at}`,
        header: 'Внимание!',
        icon: 'pi pi-exclamation-triangle',
        acceptIcon: 'pi pi-times',
        acceptLabel: 'Снять блок',
        rejectLabel: 'Продолжить просмотр',
        accept: () => {
          leaving('x')
        },
        reject: () => {},
        onHide: () => {}
      })
    }
  }
}

async function sendDocumentToClient({ htmlString, subject }: { htmlString: string; subject: string }) {
  sendMailClientModal.value = true
  _tempEmailMeta.value.message = htmlString
  _tempEmailMeta.value.subject = subject
}

function changeSnapshotHandler({ value }) {
  order.value = value.body
  orderItems.value = order.value.items
  generateOrderQr()
}

function onSuccessSendMailHandler(emailParts) {
  sendMailClientModal.value = false
  _tempEmailMeta.value = {}

  const emailLogItem = {
    ...emailParts,
    date: new Date().toLocaleString()
  }

  // if (!order.value.data?.emailLog) {
  //   order.value.data.emailLog = [emailLogItem]
  // } else {
  //   order.value.data.emailLog.push(emailLogItem)
  // }
}

watch(() => order.value.order_status, (newStatus) => {
  if (newStatus === 'Завершен' && (!order.value.order_tracknumber || order.value.order_tracknumber.trim() === '')) {
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Невозможно установить статус "Завершен" без указания трек-номера',
      life: 5000
    });
    // Возвращаем предыдущий статус
    order.value.order_status = currentSnapshot.value.body.order_status;
  }
});

async function saveChanges() {
  // Проверяем наличие трек-номера при статусе "Завершен"
  if (order.value.order_status === 'Завершен' && (!order.value.order_tracknumber || order.value.order_tracknumber.trim() === '')) {
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Невозможно сохранить заказ со статусом "Завершен" без указания трек-номера',
      life: 5000
    });
    return; // Прерываем сохранение
  }

  saveLoading.value = true
  toast.add({ severity: 'info', summary: 'Обновление заказа...', detail: '' })
  stockUpdateErrorsData.value?.stockUpdateErrors && (stockUpdateErrorsData.value.stockUpdateErrors = [])

  const res = await api('cpan/order/save', {
    method: 'POST',
    data: {
      ...order.value,
      items: orderItems.value,
      order_price: orderSums.value.order
    }
  })

  toast.removeAllGroups()

  if (res.ok) {
    if (!res.body?.stockUpdateErrors) {
      toast.add({ severity: 'success', summary: 'Заказ успешно обновлен!', detail: '', life: 3000 })
      emit('saveChanges')
      if (props.order?.order_gtd) {
        window.location.reload()
      }
    } else {
      toast.add({ severity: 'error', summary: 'Недостаточно наличия на складе', detail: 'Ошибка обновления товара', life: 3000 })
      // показать по каким товарам ошибкки
      stockUpdateErrorsData.value = res.body
    }
  } else {
    toast.add({ severity: 'error', summary: 'Серверная ошибка!', detail: res.message || res.statusText, life: 3000 })
  }
  // if empty stock error

  saveLoading.value = false
  //console.log('saveChanges res:', res)
}

function deleteOrderItem(event, orderItem) {
  confirm.require({
    target: event.currentTarget,
    group: 'orderView',
    message: `Удалить товар ${orderItem.prod_analogsku} из списка?`,
    icon: 'pi pi-exclamation-triangle',
    acceptLabel: 'Подтвердить',
    acceptClass: 'p-button-danger',
    accept: () => {
      orderItems.value = orderItems.value.filter((item) => item.prod_id !== orderItem.prod_id)
    },
    reject: () => {
      if (orderItem.orderCount === 0) {
        orderItem.orderCount = 1
      }
    }
  })
}

function transformSnapshotDT() {
  props.order?.snapshots?.map((snapshot) => (snapshot.dt = dayjs(snapshot.created_at || snapshot.date).format('DD.MM.YYYY hh:mm:ss')))
}

function orderItemChangeHandler(event, value, orderItem) {
  if (value === 0) {
    deleteOrderItem(event, orderItem)
  } else {
    orderItem.orderCount = value
  }
}

async function reCalcShippingPrice(mn = false) {
  mn &&
    toast.add({
      summary: 'Пересчет стоимости доставки...',
      severity: 'info'
    })

  const dn = {
    'Курьер': 'express',
    'Почта РФ': 'standard',
    'СДЭК': 'cdek',
    'Курьер КСЭ': 'express'
  }

  const payload = {
    type: dn[order.value.order_shipping] || 'standard',
    country: order.value.client?.client_country,
    destinationIndex: order.value.client?.client_postindex,
    orderId: order.value.order_id
  }

  const res = await api('/cpan/shipping/calc/', {
    method: 'POST',
    data: {
      ...payload
    }
  })

  mn && toast.removeAllGroups()

  if (res.ok) {
    recommendedShippingPrice.value = typeof res.body === 'object' ? 0 : res.body
  }
}

function getStatusClass() {
  return statusColorClass[order.value?.data?.order_status] || 'bg-gray-400'
}

function gotoPochtaTracking() {
  window.open(`https://www.pochta.ru/tracking#${order.value.order_tracknumber}`, '_blank').focus()
}

function addNewOrderItemHandler(item) {
  // //console.log('addNewOrderItemHandler:', item);
  orderItems.value.unshift({
    ...item,
    orderCount: 1,
    isNew: true
  })
}

const npopToggle = (event) => {
  npop.value.toggle(event)
}

const prodpopToggle = (event, product) => {
  selectedProduct.value = product
  // prodpop.value.toggle(event)
  selectedProductModal.value = !selectedProductModal.value
}

const totalPrice = computed({
  get() {
    if (!order.value?.data?.order_price) {
      return 0
    }
    return order.value.order_price + order.value.order_shippingprice
  },
  set(v) {}
})

function calculateOrderPrice(orderItems) {
  // //console.log('settingsData:', settingsData.value)

  if (!settingsData.value?.BIG_DISCOUNT_START_SUM) {
    throw new Error('settingsData: BIG_DISCOUNT_START_SUM is undefined')
  }

  if (typeof order.value.order_coupons?.personal == 'undefined') {
    order.value.order_coupons.personal = 0
  }

  let defSum = orderItems.map((item) => item.orderCount * item.prod_price).reduce((acc, val) => acc + val, 0)

  const wholesalePrices = defSum > settingsData.value?.DISCOUNT_START_SUM
  isWholesalePrices.value = wholesalePrices

  orderItems.map((item) => {
    if (typeof item.orderCount == 'undefined') {
      item.orderCount = item.item_count || item.qty
    }
  })

  // TODO: undefined settingsData handler
  let itemsSum = orderItems.map((item) => (wholesalePrices ? item.orderCount * (item.whosaleprice || item.prod_discount) : item.orderCount * item.prod_price)).reduce((acc, val) => acc + val, 0)

  let orderSum = itemsSum - itemsSum * (order.value.order_coupons.personal / 100)

  //console.log('🚀 ~ file: OrderView.vue:187 ~ calculateOrderPrice ~ orderSum:', orderSum)

  if (itemsSum > settingsData.value?.BIG_DISCOUNT_START_SUM) {
    if (settingsData.value.BIG_DISCOUNT_VALUE > order.value.order_coupons.personal) {
      orderSum = itemsSum - itemsSum * ((settingsData.value.BIG_DISCOUNT_VALUE || 10) / 100)
    }
  }

  orderSums.value = {
    def: defSum,
    wholesalePrices,
    items: itemsSum,
    order: orderSum,
    total: orderSum + order.value.order_shippingprice,
    discounts: {
      personal: itemsSum * (order.value.order_coupons.personal / 100),
      big: 0
    }
  }

  // return { orderSum: orderSum, defSum: defSum }
}

function getOrgFieldsArray(org: Object) {
  const rmf = ['org_id', 'org_client']
  return Object.keys(org).filter((i) => !rmf.includes(i))
}

async function getOrderWeight() {
  const res = await api('/service/orderweight?id=' + order.value.order_id)
  orderWeight.value = res.body
}
watch(adnlDiscount, () => {
  // let pd = order.order_coupons.personal
  let ad = adnlDiscount.value
  let ots = order.value.order_price

  order.value.order_coupons.personal = Number(((ad / ots) * 100).toFixed(2))
})

watch(
  () => [order.value?.order_coupons?.personal, orderItems.value, order.value?.order_shippingprice],
  () => {
    // order.value.order_price =
    // http://localhost:3333/cpan/order/price/25331

    calculateOrderPrice(orderItems.value)
    // reCalcShippingPrice()
  },
  { deep: true }
)

watch(order, () => {
  mounted.value = !!order.value.order_id
})

// watch(isPacked, () => {
//   if (isPacked.value) {
//     order.value.order_lastupdate_person = userData.value.user_name
//   } else {
//     order.value.order_lastupdate_person = ''
//   }
// })

function isPackedChangeHandler() {
  if (isPacked.value) {
    order.value.order_lastupdate_person = userData.value.user_name
  } else {
    order.value.order_lastupdate_person = ''
  }
}

onMounted(async () => {
  // calculateOrderPrice(order.value.items)
  order.value = props.order.data //props.order.snapshots[0].body
  currentSnapshot.value = props.order.snapshots[0]
  orderItems.value = order.value.items
  //console.log('🚀 ~ onMounted ~ orderItems.value:', orderItems.value)

  if (order.value.order_lastupdate_person) {
    isPacked.value = true
  }

  if (order.value.order_gtd) {
    isGTD.value = true
  }

  checkLock()
  calculateOrderPrice(orderItems.value)
  transformSnapshotDT()
  getOrderWeight()
  await generateOrderQr()

  const qs = new URLSearchParams(window.location.search)
  let _senderIndex = Math.floor(Math.random() * settingsData.value?.senders.length)

  senderIndex.value = qs.has('senderIndex') ? Number(qs.get('senderIndex')) : _senderIndex

  qs.set('senderIndex', String(senderIndex.value))

  const newUrl = window.location.pathname + '?' + qs.toString()
  window.history.replaceState({}, '', newUrl)

  await reCalcShippingPrice()
  // mounted.value = true
})

onBeforeUnmount(async () => {
  await leaving(false)
  // alert('unmounted!')
})
</script>

<template>
  <div v-if="order?.order_id">
    <div class="flex justify-between items-center">
      <div>
        <span v-if="props.showTitle" class="space-x-3 flex items-baseline text-lg font-semibold text-gray-600 dark:text-gray-200">
          <span>
            <span>Заказ #{{ order.order_id }}</span>
          </span>
          <span class="mt-3 text-ssm text-slate-500 dark:text-gray-300">
            <router-link :to="'/clients/' + order.client.client_id">{{ order.client.client_name }}</router-link>
            <i @click="() => (clientModalShow = true)" class="ml-2 p-2 rounded-full hover:bg-slate-200 dark:hover:bg-zinc-800 cursor-pointer pi pi-eye" />
          </span>
          <span
            :class="
              (order.order_locale == 'en' ? 'bg-red-400' : 'bg-gray-600 dark:bg-gray-600') +
              ' shadow-xl shadow-slate-200 dark:shadow-none text-base p-2 rounded-md text-white font-semibold text-center'
            "
          >
            {{ order.order_locale == 'en' ? 'RUMI' : 'RTI' }}
          </span>
          <span :class="getColorByStatus(order.order_status) + ' p-2 rounded-lg shadow-xl shadow-slate-200 dark:shadow-none text-base text-semibold text-white'"> {{ order.order_status }} </span>
          <!--  -->
        </span>
        <div :class="`flex ${order.order_gtd ? 'bg-green-100' : 'bg-slate-100'}  p-1 rounded w-32 items-center justify-center gap-4`">
          <span class="text-green-700 font-semibold">Таможня</span>
          <InputSwitch v-model="order.order_gtd" />
        </div>
      </div>
      <div v-if="!['Отменен', 'Завершен'].includes(order.order_status)" class="bg-gray-100 rounded-md p-2">
        <span :class="order.fullGtd ? 'text-green-500' : 'text-rose-500'">Полный вывоз </span> <span v-if="!order.fullGtd" class="text-rose-500">недоступен</span>
      </div>
      <div class="flex flex-wrap space-x-1">
        <Button :loading="item.loading" @click="item.command" size="small" :icon="item.icon" :severity="item.severity" text :label="item.label" :key="index" v-for="(item, index) in menuItems">
        </Button>
      </div>
    </div>
    <ClientDebt :client="order.client" />
    <Message v-if="order.order_status == 'Отменен'" severity="warn"> Для возврата <b>отмененного</b> заказа, необходимо выбрать <b>предыдущий</b> снимок заказа. </Message>
    <div class="grid md:grid-cols-2 gap-6 sm:gap-12 mt-5">
      <div class="space-y-3">
        <div>
          <span class="block-title justify-between flex items-end">
            <span>Информаия о заказе</span>
            <span class="text-base text-gray-600 dark:text-gray-200 bg-orange-50 dark:bg-zinc-600 p-2 rounded-md">
              <i class="pi pi-calendar mr-2" /> {{ dayjs(order.order_datetime).format('DD.MM.YY hh:mm:ss') }}
            </span>
          </span>

          <div class="mt-4 space-y-3 flex flex-col items-stretch bg-white dark:bg-zinc-800 shadow-lg p-2 px-4 rounded-lg">
            <div class="dark:bg-zinc-800 px-2 py-2 -mx-4 -mt-2 flex items-center justify-between">
              <div>
                <span class="el-text">Снимок: </span>
                <Dropdown class="ml-1 w-52" v-model="currentSnapshot" @change="changeSnapshotHandler" :options="props.order.snapshots" optionLabel="dt" />
              </div>
              <div class="flex items-center justify-end space-x-2">
                <div v-show="isPacked" class="bg-slate-200 px-2 py-1 rounded-lg text-sm font-bold mr-2">
                  <i class="pi pi-user font-bold mr-2"></i>
                  <span>{{ order.order_lastupdate_person }}</span>
                </div>
                <span class="font-semibold">Заказ собран: </span>
                <InputSwitch @change="isPackedChangeHandler" v-model="isPacked" />
              </div>
            </div>
            <!-- <hr /> -->
            <div class="flex flex-wrap justify-between xl:grid-cols-2">
              <div>
                <div class="space-y-2">
                  <div class="">
                    <div class="flex items-center justify-between space-x-3">
                      <span class="el-text">Статус: </span>
                      <Dropdown
                        :pt="{
                          wrapper: '!h-[450px] !max-h-[450px]'
                        }"
                        class="w-52"
                        v-model="order.order_status"
                        :options="orderStatuses"
                        placeholder="Выбрать статус"
                      />
                    </div>
                  </div>
                  <div>
                    <div class="flex items-center justify-between space-x-3">
                      <span class="el-text">Доставка: </span>
                      <Shipping :order="order" />
                    </div>
                  </div>
                  <div class="mt-3">
                    <div class="flex items-center justify-between space-x-3">
                      <span class="el-text">Оплата: </span>
                      <Payment :order="order" />
                    </div>
                  </div>
                  <div class="mt-3 flex items-center space-x-3">
                    <div class="el-text">Цены:</div>
                    <div class="px-2 p-1 rounded-md font-bold bg-yellow-100 dark:bg-yellow-600/20" v-if="isWholesalePrices">Опт</div>
                    <div class="px-2 p-1 rounded-md font-bold dark:bg-zinc-800" v-else>Розница</div>
                  </div>
                  <div class="mt-3 flex flex-col items-start space-y-3">
                    <div class="el-text">Примечание покупателя:</div>
                    <div class="text-slate-600 dark:text-zinc-100 w-60 font-semibold border border-yellow-300 rounded-md p-3">
                      {{ order.order_desc }}
                    </div>
                  </div>
                </div>
                <div class="mt-3">
                  <div class="flex items-center justify-between space-x-3">
                    <span class="el-text">Трек-номер: </span>
                    <div>
                      <div class="flex">
                        <InputText class="w-44" v-model="order.order_tracknumber" />
                        <button @click="gotoPochtaTracking" class="bg-blue-500 rounded-md text-white p-1 px-2 -ml-1 rounded-l-none"><i class="pi pi-external-link" /></button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <div class="flex justify-end">
                  <div class="space-y-3">
                    <div class="flex flex-col items-end space-y-2 justify-end">
                      <div class="flex items-center justify-between space-x-2">
                        <span class="el-text">Доставка: </span>
                        <div class="flex flex-wrap items-center space-x-3">
                          <InputNumber
                            inputClass="border-1 w-32 p-1 px-2 outline-transparent font-bold text-slate-500 dark:text-gray-300"
                            v-model="order.order_shippingprice"
                            mode="currency"
                            currency="RUB"
                            locale="ru-RU"
                          />

                          <div v-if="order.order_locale != 'ru'" class="bg-sky-100 dark:bg-sky-700/50 font-semibold p-1 px-2 rounded-md flex items-center h-11">
                            {{
                              Number(order.order_shippingprice / (!isWholesalePrices ? settingsData?.currency?.r_eur : settingsData?.currency?.eur)).toLocaleString('en-US', {
                                style: 'currency',
                                currency: 'EUR'
                              })
                            }}
                          </div>

                          <div v-if="order.order_locale != 'ru'" class="bg-red-100 dark:bg-rose-700/50 font-semibold p-1 px-2 rounded-md flex items-center h-11">
                            {{
                              Number(order.order_shippingprice / (!isWholesalePrices ? settingsData?.currency?.r_zl : settingsData?.currency?.zl)).toLocaleString('pl-PL', {
                                style: 'currency',
                                currency: 'PLN'
                              })
                            }}
                          </div>
                        </div>
                      </div>
                      <div v-show="order.order_locale == 'ru' && order.order_shippingprice !== recommendedShippingPrice" class="text-sm" v-if="recommendedShippingPrice">
                        <span class="el-text text-sm mr-2">Рекомендованная: </span>
                        <div class="flex flex-wrap items-center">
                          <span class="text-slate-500 dark:text-gray-300 font-bold">{{ toValuteString(recommendedShippingPrice) }}</span>
                          <div v-if="order.order_locale != 'ru'" class="bbg-sky-100 p-1 px-2 rounded-md flex items-center h-11">
                            {{
  Number(recommendedShippingPrice / (!isWholesalePrices ? settingsData?.currency?.r_eur : settingsData?.currency?.eur)).toLocaleString('en-US', {
                                style: 'currency',
                                currency: 'EUR'
                              })
                            }}
                          </div>

                          <div v-if="order.order_locale != 'ru'" class="bbg-red-100 p-1 px-2 rounded-md flex items-center h-11">
                            {{
  Number(recommendedShippingPrice / (!isWholesalePrices ? settingsData?.currency?.r_zl : settingsData?.currency?.zl)).toLocaleString('pl-PL', {
                                style: 'currency',
                                currency: 'PLN'
                              })
                            }}
                          </div>
                        </div>
                      </div>

                      <div class="my-1">
                        <Button size="small" class="p-1 text-xscursor-pointer transition-all ease-in-out duration-150" @click="reCalcShippingPrice" text
                          >Пересчитать <i class="ml-1 pi pi-refresh"
                        /></Button>
                      </div>
                      <div class="flex items-center space-x-2 justify-between">
                        <span class="el-text">Скидка: </span>
                        <span>
                          <InputNumber
                            suffix="%"
                            _min="0"
                            :step="0.1"
                            :unstyled="false"
                            inputClass="border-1 h-10 bg-slate-200 rounded-md p-2 w-32 outline-transparent font-semibold text-gray-500 dark:text-gray-200"
                            v-model="order.order_coupons.personal"
                            locale="ru-RU"
                          />
                        </span>
                      </div>
                      <div class="flex items-center justify-between">
                        <span class="el-text mr-1">Сумма заказа: </span>
                        <div class="flex space-x-2 items-center">
                          <div class="flex flex-wrap items-center space-x-3">
                            <span class="text-slate-500 dark:text-gray-300 font-bold">{{ toValuteString(orderSums.order) }}</span>

                            <div v-if="order.order_locale != 'ru'" class="bg-sky-100 dark:bg-sky-700/50 font-semibold p-1 px-2 rounded-md font-semibold flex items-center h-11">
                              {{ Number(orderSums.order / (!isWholesalePrices ? settingsData?.currency?.r_eur : settingsData?.currency?.eur)).toLocaleString('en-US', { style: 'currency', currency: 'EUR' }) }}
                            </div>

                            <div v-if="order.order_locale != 'ru'" class="bg-red-100 dark:bg-rose-700/50 p-1 px-2 rounded-md font-semibold flex items-center h-11">
                              {{ Number(orderSums.order / (!isWholesalePrices ? settingsData?.currency?.r_zl : settingsData?.currency?.zl)).toLocaleString('pl-PL', { style: 'currency', currency: 'PLN' }) }}
                            </div>

                            <span v-tooltip.top="'Текущая сумма заказа отличается от сохраненной'">
                              <i v-show="order.order_price !== orderSums.order" class="pi pi-exclamation-triangle text-red-400 text-lg ml-1" />
                            </span>
                          </div>
                        </div>
                      </div>
                      <div v-show="order.order_price !== orderSums.order" class="text-sm">
                        <span class="el-text text-xs mr-2">Сумма сохраненная: </span>
                        <span>
                          <span class="text-slate-500 dark:text-gray-300 text-xs font-bold">{{ toValuteString(order.order_price) }}</span>
                        </span>
                      </div>
                    </div>
                    <div>
                      <div class="flex flex-col items-end space-y-2 justify-center">
                        <div v-show="order.order_coupons?.personal">
                          <span class="el-text">Скидка: </span>
                          <span class="text-slate-500 dark:text-gray-300 font-bold"> {{ toValuteString(orderSums.items - orderSums.order) }}</span>
                        </div>
                        <div v-show="order.order_coupons?.personal">
                          <span class="el-text text-xs">Сумма без скидки: </span>
                          <span class="text-slate-500 dark:text-gray-300 text-xs font-bold">{{ toValuteString(orderSums.items) }}</span>
                        </div>
                        <div class="pt-0">
                          <span class="zel-text mr-2 text-sky-500 text-lg font-bold">Итого: </span>
                          <div class="flex flex-wrap space-x-3 items-center">
                            <span class="text-slate-500 dark:text-gray-300 font-bold text-lg">{{ toValuteString(orderSums.total) }}</span>

                            <div v-if="order.order_locale != 'ru'" class="bg-sky-100 dark:bg-sky-700/50 font-semibold p-1 px-2 rounded-md font-semibold flex items-center h-11">
                              {{ Number(orderSums.total / (!isWholesalePrices ? settingsData?.currency?.r_eur : settingsData?.currency?.eur)).toLocaleString('en-US', { style: 'currency', currency: 'EUR' }) }}
                            </div>

                            <div v-if="order.order_locale != 'ru'" class="bg-red-100 dark:bg-rose-700/50 font-semibold p-1 px-2 rounded-md font-semibold flex items-center h-11">
                              {{ Number(orderSums.total / (!isWholesalePrices ? settingsData?.currency?.r_zl : settingsData?.currency?.zl)).toLocaleString('pl-PL', { style: 'currency', currency: 'PLN' }) }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="space-y-2">
              <div class="space-x-3">
                <span class="el-text">Вес: </span>
                <span>
                  <InputNumber
                    :min="0"
                    inputClass="border-1 w-40 text-sm text-lg outline-transparent font-semibold text-gray-500 dark:text-gray-200"
                    v-model="order.order_weight"
                    locale="ru-RU"
                    suffix=" гр."
                    mode="decimal"
                  />
                  <span v-if="order.order_weight != orderWeight" class="text-xs ml-2 font-semibold italic text-slate-500">( {{ orderWeight }} гр.)</span>
                </span>
              </div>
              <div>
                <Panel
                  :pt="{
                    header: '!p-1 !px-2 text-sm flex items-center'
                  }"
                  class="mt-4"
                  header="Дополнительно"
                  :toggleable="true"
                  :collapsed="true"
                >
                  <div>
                    <span class="el-text">Рассчитать скидку от: </span>
                    <span>
                      <InputNumber
                        :min="0"
                        inputClass="border-1 w-40 text-lg outline-transparent font-semibold text-gray-500 dark:text-gray-200"
                        v-model="adnlDiscount"
                        locale="ru-RU"
                        mode="currency"
                        currency="RUB"
                      />
                    </span>
                  </div>
                </Panel>
              </div>
            </div>
          </div>
        </div>

        <div class="pt-3">
          <span class="block-title">E-mail</span>
          <div class="text-gray-600 dark:text-gray-200 dark:text-gray-200 mt-2 bg-white dark:bg-zinc-800 shadow-lg p-2 rounded-lg">
            <ClientMailBox :clientemail="order.client.client_mail" />
          </div>
        </div>
        <div class="pt-3">
          <span class="block-title">Лог изменений заказа</span>
          <div class="text-gray-600 dark:text-gray-200 dark:text-gray-200 mt-2 bg-white dark:bg-zinc-800 shadow-lg p-2 rounded-lg">
            <ScrollPanel class="custombar1" style="width: 100%; height: 400px">
              <OrderSnapshotsLog :data="props.order.snapshots" />
            </ScrollPanel>
          </div>
        </div>
      </div>

      <div id="dcv" class="space-y-3">
        <div>
          <div class="flex items-center justify-between">
            <span class="block-title">Получатель</span>
            <div class="flex space-x-3 items-center">
              <Button @click="(e) => (clientEditOp.toggle(e), (showClientEditNotice = true))" icon="pi pi-user-edit" text label="Редактировать" />
              <!-- <Button @click="() => (callClientModal = true)" icon="pi pi-phone" text label="Позвонить" /> -->
              <Button @click="() => (sendMailClientModal = true)" icon="pi pi-at" text label="Email" />
            </div>
          </div>
          <div class="mt-2 bg-white dark:bg-zinc-800 shadow-lg p-2 rounded-lg">
            <OverlayPanel appendTo="#dcv" ref="clientEditOp">
              <ClientData mode="order" :client="order.client" />
            </OverlayPanel>

            <div class="flex justify-between items-start text-gray-600 dark:text-gray-200">
              <div>
                <div>
                  Номер клиента: <b>{{ order.client.client_number }}</b>
                </div>
                <div>
                  ФИО: <b>{{ order.client.client_name }}</b>
                </div>
                <div>
                  E-mail: <b>{{ order.client.client_mail }}</b>
                </div>
                <div>
                  Телефон: <b>{{ order.client.client_phone }}</b>
                </div>
                <div>
                  Адрес: <b>{{ order.client.client_country }}</b
                  >, <b>{{ order.client.client_city }}</b
                  >, <b>{{ order.client.client_street }}</b
                  >, д.<b>{{ order.client.client_house }}</b> кв.<b>{{ order.client.client_flat }}</b
                  >. Индекс: <b>{{ order.client.client_postindex }}</b>
                </div>
                <div>
                  СДЭК ID: <b>{{ order.client.client_cdekid }}</b>
                </div>
                <div class="mt-2 border border-slate-200 p-2 rounded-md" v-if="order.client?.org">
                  <div :body-style="{ padding: '5px' }">
                    <div v-for="(key, index) in getOrgFieldsArray(order.client.org)" :key="index">
                      <div>
                        {{ trnColumns(key) }}: <b>{{ order.client?.org[key] }}</b>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-if="showClientEditNotice" class="text-xs bg-orange-100 p-2 mt-2 rounded font-semibold text-orange-700">
                  <i class="mr-1 pi pi-exclamation-triangle" /><span class="text-orange-700">Внимание!</span> Все данные получателя относятся только к текущему заказу.
                </div>
              </div>
              <!-- <div class="flex justify-end">
                <Button size="large" text icon="pi pi-user-edit"> </Button>
              </div> -->
            </div>
          </div>
        </div>
        <div>
          <span class="block-title">Документы</span>
          <div class="mt-3 bg-white dark:bg-zinc-800 shadow-xl shadow-slate-200 dark:shadow-none rounded-md">
            <OrderDocuments :sender-index="senderIndex" @onSend="sendDocumentToClient" :order-id="order.order_id" />
          </div>
        </div>

        <div>
          <span class="block-title">Пометки по заказу</span>
          <div class="mt-3">
            <Textarea v-model="order.order_notice" class="w-full"></Textarea>
          </div>
        </div>
        <div v-if="stockUpdateErrorsData?.stockUpdateErrors?.length > 0" class="flex flex-col space-y-3">
          <Message severity="error" :closable="false" :key="index" class="flex space-x-2" v-for="(item, index) in stockUpdateErrorsData.stockUpdateErrors">
            Товар: <b>{{ item.product.prod_analogsku }}</b> Запрос: <b>{{ item.value }}</b
            >, наличие: <b>{{ item.currentStock }}</b>
          </Message>
        </div>

        <div v-if="!order.order_gtd">
          <OrderItemList
            :orderItems="orderItems"
            :npop="npop"
            :isWholesalePrices="isWholesalePrices"
            :npopToggle="npopToggle"
            :prodpopToggle="prodpopToggle"
            :orderItemChangeHandler="orderItemChangeHandler"
            :deleteOrderItem="deleteOrderItem"
          />
        </div>
        <div v-else class="flex flex-col gap-4">
          <div class="p-2 rounded bg-red-50">
            <OrderItemList
              :orderItems="orderItems.filter((i) => i.notExported)"
              :npop="npop"
              :isWholesalePrices="isWholesalePrices"
              :npopToggle="npopToggle"
              :prodpopToggle="prodpopToggle"
              :orderItemChangeHandler="orderItemChangeHandler"
              :deleteOrderItem="deleteOrderItem"
              label="Недоступны для вывоза "
            />
          </div>
          <div class="p-2 rounded bg-green-50">
            <div>
              <OrderSpecs :order="order"></OrderSpecs>
            </div>
            <OrderItemList
              :orderItems="orderItems.filter((i) => !i.notExported)"
              :npop="npop"
              :isWholesalePrices="isWholesalePrices"
              :npopToggle="npopToggle"
              :prodpopToggle="prodpopToggle"
              :orderItemChangeHandler="orderItemChangeHandler"
              :deleteOrderItem="deleteOrderItem"
              label="Декларируемые "
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <Dialog dismissableMask modal header="Позвонить клиенту" v-model:visible="callClientModal">
    <Caller @on-success="callClientModal = false" :phone="order.client?.client_phone" />
  </Dialog>

  <Dialog dismissableMask modal header="Написать клиенту" v-model:visible="sendMailClientModal">
    <SendMail :message="_tempEmailMeta.message" :subject="_tempEmailMeta.subject" @on-success="onSuccessSendMailHandler" :orderId="order.order_id" :to="order.client?.client_mail" />
  </Dialog>

  <Dialog dismissableMask modal header="Карточка клиента" v-model:visible="clientModalShow">
    <ClientCard :data="props.order.data.client" />
  </Dialog>

  <OverlayPanel ref="npop">
    <div class="el-text">Добавить товар в заказ:</div>
    <div class="mt-3">
      <FindProduct @select="addNewOrderItemHandler" />
    </div>
  </OverlayPanel>

  <ConfirmPopup group="orderView"> </ConfirmPopup>
  <ConfirmDialog></ConfirmDialog>
  <!-- <OverlayPanel
    :pt="{
      root: {
        class: 'w-3/4'
      }
    }"
    showCloseIcon
    ref="prodpop"
  >
    <div>
      <ProductInfoCard :productData="selectedProduct" />
    </div>
  </OverlayPanel> -->

  <Dialog v-model:visible="selectedProductModal" modal :header="selectedProduct?.prod_analogsku">
    <div class="flex gap-10 flex-wrap lg:flex-nowrap">
      <div class="lg:w-1/2">
        <div class="text-lg mb-3 text-sky-600">Cнимок от {{ dayjs(order?.order_datetime).format('DD.MM.YYYY') }}</div>
        <div>
          <ProductInfoCard :productData="selectedProduct" />
        </div>
      </div>
      <div class="lg:w-1/2">
        <div class="text-lg mb-3 text-sky-600">Текущее состояние</div>
        <div v-if="selectedProduct?.currentState?.prod_id">
          <ProductInfoCard show-link :productData="selectedProduct.currentState" />
        </div>
      </div>
    </div>
  </Dialog>
</template>

<Toast group="loading" />

<style>
.block-title {
  @apply text-gray-500 dark:text-gray-200 font-semibold text-lg;
}

.el-text {
  @apply text-gray-500 dark:text-gray-200 font-semibold;
}

.p-megamenu {
  @apply border-0 bg-white dark:bg-zinc-800 shadow-lg;
}

.p-megamenu .p-menuitem-link {
  @apply rounded-md;
}

.p-panel .p-panel-header {
  @apply bg-gray-100 border-0 rounded-md;
}

.p-panel .p-panel-content {
  @apply bg-gray-100 border-0 -mt-1;
}
</style>
