<script setup lang="ts">
import { api } from '@/lib/_api'
import type { Client } from '@/lib/interfaces/Order'
import Calendar from 'primevue/calendar'
import { useToast } from 'primevue/usetoast'
import { nextTick, onMounted, ref } from 'vue'

import Dropdown from 'primevue/dropdown'
import But<PERSON> from 'primevue/button'
import Dialog from 'primevue/dialog'

const props = defineProps<{
  clientId: Number
}>()

const dates = ref<Array<Date>>([])
const toast = useToast()

const formBody = ref('')
const formName = ref('reconact_rti')
const formList = ref([])
const previewModalShow = ref(false)
const emit = defineEmits(['onPrint'])


async function loadFormList() {
  const res = await api('cpan/forms/list')
  formList.value = res.body.filter((f) => f.page_key.includes('reconact'))
}

async function loadForm() {
  const { body, statusText } = await api(`/cpan/forms/make?key=${formName.value?.page_key}&clientId=${props.clientId}&dates=${dates.value.map((d) => d.toJSON()).join(',')}`)

  if (body?.html) {
    formBody.value = `${body.html}`
    previewModalShow.value = true
  } else {
    toast.add({
      severity: 'error',
      'summary': `Ошибка формирования шаблона "Акт сверки" (${formName.value}): ` + statusText
    })
  }
}

async function printElement(elementId: string) {
  const elementToPrint = document.getElementById(elementId)
  const printContainer = document.getElementById('print-container')

  emit('onPrint')
  await nextTick()

  if (elementToPrint && printContainer) {
    printContainer.innerHTML = elementToPrint.innerHTML
    previewModalShow.value = false
    await nextTick()
    window.print()
    printContainer.innerHTML = ''
  } else {
    throw new Error('print error: !elementToPrint || !printContainer')
  }
}

onMounted(async () => {
  await loadFormList()
})
</script>

<template>
  <div class="my-5 space-y-5">
    <div class="flex items-center space-x-3">
      <span class="font-semibold">Форма: </span>
      <div>
        <div class="card flex justify-content-center">
          <Dropdown v-model="formName" :options="formList" optionLabel="page_title" placeholder="Выбор формы" />
        </div>
      </div>
    </div>
    <div class="flex items-center space-x-3">
      <span class="font-semibold">Диапазон: </span>
      <Calendar dateFormat="dd.mm.yy" :pt="{}" class="w-60" v-model="dates" selectionMode="range" :manualInput="false" />
    </div>
    <div class="flex justify-end">
      <Button label="Сформировать" @click="loadForm" />
    </div>

    <div v-if="previewModalShow">
      <Dialog v-model:visible="previewModalShow" draggable :showHeader="true" modal dismissableMask header="" :style="{ width: '80vw' }">
        <div class="flex justify-end">
          <Button @click="() => printElement('formpreview')" icon="pi pi-print" size="small" text label="Печать" />
        </div>
        <div class="mt-2 bg-white dark:bg-zinc-200" id="formpreview" contenteditable="true" v-html="formBody"></div>
      </Dialog>
    </div>
  </div>
</template>
