import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import type { Categories } from '@/lib/interfaces/ApiSettings'

interface AppStore {
  categories: Categories
}

export const useAppStore = defineStore('appstore', () => {
  const data = ref<AppStore>()
  const setAppStoreData = (payload: AppStore) => (data.value = payload)
  const appStoreData = computed(() => data)

  return { appStoreData, setAppStoreData }
})
