<script lang="ts" setup>
import CountersBlock from '@/components/CountersBlock.vue'
import DebtorsList from '@/components/DebtorsList.vue'
import LastOrders from '@/components/LastOrders.vue'
import POrders from '@/components/POrders.vue'
import ProductsWithoutPhoto from '@/components/ProductsWithoutPhoto.vue'
import TopSalesProducts from '@/components/TopSalesProducts.vue'
import ZadarmaCallList from '@/components/ZadarmaCallList.vue'
import { onMounted } from 'vue'

onMounted(() => {
  try {
    document.title = 'Мир Сальников'
  } catch (error) {}
})
</script>

<template>
  <div class="mt-5">
    <CountersBlock />
  </div>
  <div class="flex flex-wrap lg:flex-nowrap items-start gap-10 mt-10">
    <div class="w-2/3 space-y-10">
      <div class="bcard p-3">
        <ZadarmaCallList class="mt-3" />
      </div>
      <div class="bcard p-3">
        <LastOrders />
      </div>
    </div>
    <div class="space-y-10">
      <div class="bcard p-3"><POrders /></div>
      <div class="bcard p-3 flex justify-between">
        <div>
          <ProductsWithoutPhoto />
        </div>
        <div class="w-full">
          <TopSalesProducts />
        </div>
      </div>
      <div class="bcard p-3">
        <DebtorsList />
      </div>
    </div>
  </div>
</template>
