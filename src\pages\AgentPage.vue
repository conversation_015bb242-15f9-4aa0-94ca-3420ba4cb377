<template>
  <div class="h-[calc(100vh-120px)] flex bg-gray-50">
    <!-- Проверка на ошибки загрузки -->
    <div v-if="loadingError" class="w-full flex items-center justify-center">
      <div class="text-center p-8">
        <i class="pi pi-exclamation-triangle text-4xl text-red-500 mb-4"></i>
        <h2 class="text-xl font-semibold text-gray-900 mb-2">Ошибка загрузки</h2>
        <p class="text-gray-600 mb-4">{{ loadingError }}</p>
        <Button @click="retryLoad" severity="secondary">
          <i class="pi pi-refresh mr-2"></i>
          Повторить
        </Button>
      </div>
    </div>

    <!-- Основной интерфейс -->
    <template v-else>
      <!-- Боковая панель с сессиями -->
      <SessionList />

      <!-- Основная область чата -->
      <div class="flex-1 flex flex-col">
        <ChatInterface />
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import { useAgentStore } from '@/stores/agent'
import SessionList from '@/components/agent/SessionList.vue'
import ChatInterface from '@/components/agent/ChatInterface.vue'
import Button from 'primevue/button'

const agentStore = useAgentStore()
const loadingError = ref<string | null>(null)

// Инициализация при монтировании страницы
onMounted(async () => {
  try {
    // Загружаем сессии при входе на страницу
    await agentStore.loadSessions()
  } catch (error) {
    console.error('Ошибка инициализации страницы агента:', error)
    loadingError.value = error instanceof Error ? error.message : 'Неизвестная ошибка'
  }
})

// Очистка при размонтировании (опционально)
onUnmounted(() => {
  // Можно очистить ошибки при уходе со страницы
  agentStore.clearErrors()
})

const retryLoad = async () => {
  loadingError.value = null
  try {
    await agentStore.loadSessions()
  } catch (error) {
    console.error('Ошибка повторной загрузки:', error)
    loadingError.value = error instanceof Error ? error.message : 'Неизвестная ошибка'
  }
}
</script>

<style scoped>
/* Обеспечиваем правильную высоту для полноэкранного интерфейса */
.h-\[calc\(100vh-120px\)\] {
  height: calc(100vh - 120px);
}

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
  .h-\[calc\(100vh-120px\)\] {
    height: calc(100vh - 80px);
  }
}
</style>
