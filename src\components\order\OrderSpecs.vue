<script setup lang="ts">
import { defineProps, onMounted, ref, watch, computed, onUnmounted, onBeforeUnmount } from 'vue'
import Accordion from 'primevue/accordion'
import AccordionTab from 'primevue/accordiontab'

const props = defineProps({
  order: { required: true, default: {} }
})
//console.log('🚀 ~ order:', props.order)

// const props = defineProps<{
//     value: string
// }>()
</script>

<template>
  <div>
    <Accordion :activeIndex="1">
      <AccordionTab header="Список спецификаций">
        <ol class="">
          <li v-for="spec in props.order.specs || []" :key="spec">
            <b>{{ spec[0].spec_id }} </b>. <span>{{ spec[0].spec_title }}</span>
          </li>
        </ol>
      </AccordionTab>
    </Accordion>
  </div>
</template>
