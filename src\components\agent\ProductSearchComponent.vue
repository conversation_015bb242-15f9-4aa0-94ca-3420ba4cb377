<template>
  <div class="product-search-component">
    <!-- Поиск товаров -->
    <div class="space-y-4">
      <div class="flex gap-2">
        <InputText
          v-model="searchQuery"
          class="flex-1"
          placeholder="Введите название товара, артикул или производителя"
          @keyup.enter="searchProducts"
        />
        <Button
          @click="searchProducts"
          :loading="isSearching"
          icon="pi pi-search"
          label="Найти"
        />
      </div>
      
      <!-- Переключатель типа поиска -->
      <div class="flex items-center gap-4">
        <div class="flex items-center">
          <Checkbox
            v-model="useDeepSearch"
            inputId="deepSearch"
            binary
          />
          <label for="deepSearch" class="ml-2 text-sm">
            Глубокий поиск (медленнее, но точнее)
          </label>
        </div>
        
        <div v-if="searchResults.length > 0" class="text-sm text-gray-600">
          Найдено: {{ searchResults.length }} товаров
        </div>
      </div>
    </div>
    
    <!-- Результаты поиска -->
    <div v-if="isSearching" class="flex justify-center py-8">
      <ProgressSpinner size="50px" />
    </div>
    
    <div v-else-if="searchResults.length > 0" class="mt-6">
      <div class="grid grid-cols-1 gap-4 max-h-96 overflow-y-auto">
        <div
          v-for="product in searchResults"
          :key="product.prod_id"
          class="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
          @click="selectProduct(product)"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <!-- Основная информация -->
              <div class="space-y-2">
                <div class="font-medium text-lg">{{ product.prod_analogsku }}</div>
                
                <div class="text-sm text-gray-600">
                  {{ product.prod_purpose }}
                </div>
                
                <div class="flex flex-wrap gap-4 text-xs text-gray-500">
                  <span v-if="product.prod_manuf">
                    <i class="pi pi-building mr-1"></i>
                    {{ product.prod_manuf }}
                  </span>
                  
                  <span v-if="product.prod_type">
                    <i class="pi pi-tag mr-1"></i>
                    {{ product.prod_type }}
                  </span>
                  
                  <span v-if="product.prod_size">
                    <i class="pi pi-expand mr-1"></i>
                    {{ product.prod_size }}
                  </span>
                  
                  <span v-if="product.prod_sku">
                    <i class="pi pi-barcode mr-1"></i>
                    {{ product.prod_sku }}
                  </span>
                </div>
                
                <!-- Дополнительная информация -->
                <div v-if="product.prod_description" class="text-sm text-gray-600 mt-2">
                  {{ truncateText(product.prod_description, 150) }}
                </div>
              </div>
            </div>
            
            <!-- Цена и кнопка выбора -->
            <div class="flex flex-col items-end gap-2 ml-4">
              <div class="text-right">
                <div class="text-lg font-bold text-green-600">
                  {{ formatPrice(product.prod_price) }} ₽
                </div>
                
                <div v-if="product.prod_currency && product.prod_currency !== 'RUB'" class="text-xs text-gray-500">
                  {{ product.prod_currency }}
                </div>
              </div>
              
              <Button
                @click.stop="selectProduct(product)"
                size="small"
                icon="pi pi-plus"
                label="Выбрать"
                class="whitespace-nowrap"
              />
            </div>
          </div>
          
          <!-- Индикатор наличия -->
          <div v-if="product.prod_stock !== undefined" class="mt-3 pt-3 border-t">
            <div class="flex items-center gap-2">
              <i 
                :class="[
                  'pi',
                  product.prod_stock > 0 ? 'pi-check-circle text-green-500' : 'pi-times-circle text-red-500'
                ]"
              ></i>
              <span class="text-sm" :class="product.prod_stock > 0 ? 'text-green-600' : 'text-red-600'">
                {{ product.prod_stock > 0 ? `В наличии: ${product.prod_stock} шт.` : 'Нет в наличии' }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Состояние отсутствия результатов -->
    <div v-else-if="hasSearched && !isSearching" class="text-center py-8">
      <div class="text-gray-500">
        <i class="pi pi-search text-4xl mb-4 block"></i>
        <div class="text-lg mb-2">Товары не найдены</div>
        <div class="text-sm">Попробуйте изменить поисковый запрос или включить глубокий поиск</div>
      </div>
    </div>
    
    <!-- Начальное состояние -->
    <div v-else class="text-center py-8">
      <div class="text-gray-400">
        <i class="pi pi-search text-4xl mb-4 block"></i>
        <div class="text-lg mb-2">Поиск товаров</div>
        <div class="text-sm">Введите название товара, артикул или производителя для поиска</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useToast } from 'primevue/usetoast'
import InputText from 'primevue/inputtext'
import Button from 'primevue/button'
import Checkbox from 'primevue/checkbox'
import ProgressSpinner from 'primevue/progressspinner'
import { api } from '@/lib/_api'

interface Product {
  prod_id: string
  prod_sku: string
  prod_analogsku: string
  prod_price: number
  prod_currency?: string
  prod_manuf: string
  prod_type: string
  prod_size: string
  prod_purpose: string
  prod_description?: string
  prod_stock?: number
}

interface Emits {
  (e: 'select', product: Product): void
}

const emit = defineEmits<Emits>()
const toast = useToast()

// Состояние поиска
const searchQuery = ref('')
const searchResults = ref<Product[]>([])
const isSearching = ref(false)
const hasSearched = ref(false)
const useDeepSearch = ref(false)

// Методы
const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('ru-RU', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(price)
}

const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const searchProducts = async () => {
  if (!searchQuery.value.trim()) {
    toast.add({
      severity: 'warn',
      summary: 'Предупреждение',
      detail: 'Введите поисковый запрос'
    })
    return
  }
  
  isSearching.value = true
  hasSearched.value = true
  
  try {
    const endpoint = useDeepSearch.value ? '/search/global' : '/search/fast'
    const response = await api(`${endpoint}/${searchQuery.value.trim()}`)
    
    if (response.ok && response.body) {
      const products = response.body.products || [];
      searchResults.value = products.map((item: any) => ({
        prod_id: item.prod_id,
        prod_sku: item.prod_sku,
        prod_analogsku: item.prod_analogsku,
        prod_cat: item.prod_cat,
        prod_weight: item.prod_weight,
        prod_price: item.prod_price,
        prod_count: item.prod_count,
        prod_manuf: item.prod_manuf,
        prod_note: item.prod_note,
        prod_year: item.prod_year,
        prod_type: item.prod_type,
        prod_uses: item.prod_uses,
        prod_size: item.prod_size,
        prod_discount: item.prod_discount,
        prod_purpose: item.prod_purpose,
        prod_analogs: item.prod_analogs,
        prod_material: item.prod_material,
        prod_rk: item.prod_rk,
        prod_group: item.prod_group,
        prod_group_count: item.prod_group_count,
        prod_img: item.prod_img,
        prod_img_rumi: item.prod_img_rumi,
        prod_images: item.prod_images,
        prod_model: item.prod_model,
        prod_buy_limit: item.prod_buy_limit,
        qty: item.qty,
        whosaleprice: item.whosaleprice,
        buy_limit: item.buy_limit,
        images: item.images || []
       }))
    } else {
      searchResults.value = []
    }
    
    if (searchResults.value.length === 0) {
      toast.add({
        severity: 'info',
        summary: 'Информация',
        detail: 'По вашему запросу товары не найдены'
      })
    }
  } catch (error) {
    console.error('Ошибка поиска товаров:', error)
    searchResults.value = []
    
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось выполнить поиск товаров'
    })
  } finally {
    isSearching.value = false
  }
}

const selectProduct = (product: Product) => {
  emit('select', product)
  
  toast.add({
    severity: 'success',
    summary: 'Успешно',
    detail: `Товар "${product.prod_analogsku}" выбран`
  })
}
</script>

<style scoped>
.product-search-component {
  min-height: 400px;
}

.product-search-component :deep(.p-progressspinner) {
  width: 50px;
  height: 50px;
}
</style>