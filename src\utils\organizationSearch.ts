import type { OrgDetails, FindOrgResponse } from '@/components/agent/tool_prototype'

/**
 * Интерфейс для данных организации в формате приложения
 */
export interface OrganizationData {
  org_name: string
  org_adress: string
  org_inn: string
  org_kpp: string
  org_rschet: string
  org_kschet: string
  org_bik: string
  org_bank: string
}

/**
 * Поиск организации по ИНН или названию
 * @param query - ИНН или название организации
 * @returns Promise с данными организации
 */
export async function searchOrganization(query: string): Promise<OrganizationData | null> {
  try {
    if (!query || query.trim().length === 0) {
      throw new Error('Необходимо указать название или ИНН организации')
    }

    const encodedValue = encodeURIComponent(query.trim())
    const apiUrl = `https://mirsalnikov.ru/api/service/findorg/${encodedValue}`

    console.log('🔍 Поиск организации по значению:', query)
    console.log('📡 URL запроса:', apiUrl)

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'accept': '*/*',
        'accept-language': 'ru-RU,ru;q=0.9,en-GB;q=0.8,en;q=0.7,en-US;q=0.6,es;q=0.5',
        'content-type': 'application/json',
        'priority': 'u=1, i',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Linux"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin'
      },
      signal: AbortSignal.timeout(15000) // 15 секунд timeout
    })

    console.log('📡 Статус ответа:', response.status)

    if (!response.ok) {
      throw new Error(`Ошибка сервера при поиске: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    console.log('🚀 Данные организации:', data)

    // Валидация структуры ответа
    if (!data || typeof data !== 'object') {
      throw new Error('Получен некорректный ответ от сервера поиска')
    }

    // Обрабатываем ответ в зависимости от структуры API
    if (Array.isArray(data) && data.length > 0) {
      const orgData = data[0] // Берем первую найденную организацию
      return convertToOrganizationData(orgData)
    } else {
      console.log(`ℹ️ Организация "${query}" не найдена`)
      return null
    }

  } catch (error) {
    console.error('❌ Ошибка при поиске организации:', error)
    
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        throw new Error('Превышено время ожидания при поиске организации')
      }
      
      if (error.message.includes('fetch')) {
        throw new Error('Ошибка сети при поиске организации. Проверьте подключение к интернету')
      }
    }
    
    throw error
  }
}

/**
 * Конвертирует данные из API в формат приложения
 * @param apiData - данные из API
 * @returns данные организации в формате приложения
 */
function convertToOrganizationData(apiData: any): OrganizationData {
  const data = apiData.data || apiData

  return {
    org_name: data.name?.short_with_opf || data.name?.full_with_opf || '',
    org_inn: data.inn || '',
    org_kpp: data.kpp || '',
    org_adress: data.address?.value || data.address?.unrestricted_value || '',
    org_rschet: '', // Эти данные обычно не приходят из API поиска
    org_kschet: '',
    org_bik: '',
    org_bank: ''
  }
}

/**
 * Проверяет, является ли строка ИНН
 * @param value - строка для проверки
 * @returns true, если строка похожа на ИНН
 */
export function isINN(value: string): boolean {
  // ИНН может быть 10 или 12 цифр
  return /^\d{10}$|^\d{12}$/.test(value.trim())
}

/**
 * Форматирует строку поиска для лучших результатов
 * @param query - исходная строка поиска
 * @returns отформатированная строка
 */
export function formatSearchQuery(query: string): string {
  const trimmed = query.trim()
  
  // Если это ИНН, возвращаем как есть
  if (isINN(trimmed)) {
    return trimmed
  }
  
  // Для названий убираем лишние кавычки и пробелы
  return trimmed.replace(/["']/g, '').replace(/\s+/g, ' ')
}