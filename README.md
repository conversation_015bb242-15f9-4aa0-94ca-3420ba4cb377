# Vue.js 3 TypeScript Boilerplate

This Vue.js 3 boilerplate is written in TypeScript and includes Pinia, Vue Router and Tailwind CSS.

## Customize configuration

See [Tailwind CSS Configuration](https://tailwindcss.com/docs/configuration).
See [Vite Configuration Reference](https://vitejs.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
vite
npm run dev
```

### Locally preview build

```sh
vite preview
npm run preview
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```

### Build only

```sh
vite build
npm run build-only
```

### Lint with [ESLint](https://eslint.org/)

```sh
npm run lint
```

### Check types

```sh
npm run type-check
```
