<script setup lang="ts">
import { useQuery, useQueryClient } from '@tanstack/vue-query'
import { ref } from 'vue'
import Cookies from 'js-cookie'
import { useUserStore } from '@/stores/user'
import { api } from '@/lib/_api'
import InputText from 'primevue/inputtext'
import Password from 'primevue/password'
import But<PERSON> from 'primevue/button'
import ProgressSpinner from 'primevue/progressspinner'
import { useToast } from 'primevue/usetoast'

const queryClient = useQueryClient()

const { userData, setUserData } = useUserStore()

const login = ref('')
const password = ref('')
const code = ref()
const toast = useToast()

const appver = ref('0.9.0')
const authLoading = ref(false)
const isCodeMode = ref(false)

const {
  isLoading: check_isLoading,
  isSuccess: check_isFetching,
  isError: chck_isError,
  data: check_data,
  error: check_error,
  refetch: check_refetch
} = useQuery({
  queryKey: [],
  queryFn: checkAuth,
  // onError: (e) => apiErrorToast(e),
  refetchOnWindowFocus: true,
  retry: 0,
  onSuccess: (data) => {
    setUserData(data)
    Cookies.set('rti-appver', appver.value, {
      domain: 'mirsalnikov.ru'
    })
  }
})

async function checkAuth() {
  const u = await api('cpan/auth/check')
  if (u.ok && u.body?.user_id) {
    if (window.localStorage.getItem('token')) {
      Cookies.set('ctoken', window.localStorage.getItem('token') ?? '', {
        path: '/',
        domain: 'mirsalnikov.ru'
      })
    }

    return u.body
  } else {
    throw new Error('401')
  }
}

async function auth() {
  authLoading.value = true
  const res = await api('/cpan/auth/login', {
    method: 'POST',
    data: {
      login: login.value,
      password: password.value,
      code: code.value
    }
  })

  if (res.ok) {
    if (res.body.requestcode) {
      isCodeMode.value = true
    } else {
      window.localStorage.setItem('token', res.body.token)
      check_refetch()

      Cookies.set('rti-appver', appver.value, {
        path: '/',
        domain: 'mirsalnikov.ru'
      })

      Cookies.set('ctoken', res.body.token, {
        path: '/',
        domain: 'mirsalnikov.ru'
      })
    }
  } else {
    toast.add({ severity: 'error', summary: 'Ошибка: ', detail: res.message || res.statusText, life: 3000 })
  }

  authLoading.value = false

  // //console.log('auth res:', res);
}
</script>

<template>
  <Toast />
  <div v-if="!check_isLoading" class="space-y-3 bcard p-10">
    <h3 class="font-bold text-xl">Авторизация</h3>
    <div v-if="!isCodeMode" class="space-y-7">
      <div>
        <span class="flex items-center">
          <InputText v-model="login" placeholder="Логин" />
          <i class="pi pi-user -ml-6" />
        </span>
      </div>
      <div>
        <span class="flex items-center">
          <div>
            <Password v-model="password" :feedback="false" toggle-mask />
          </div>
          <i class="pi pi-password -ml-5" />
        </span>
      </div>
    </div>
    <div v-if="isCodeMode">
      <span class="flex items-center">
        <InputText v-model="code" placeholder="Код" />
        <i class="pi pi-shield -ml-5" />
      </span>
    </div>
    <div class="pt-5 flex justify-end">
      <Button :loading="authLoading" @click="auth" text icon="pi pi-sign-in" label="Войти" />
    </div>
  </div>
  <div class="flex flex-col items-center justify-center h-screen" v-else>
    <div class="text-slate-400 font-bold text-lg">Проверка авторизации</div>
    <ProgressSpinner />
  </div>
</template>
