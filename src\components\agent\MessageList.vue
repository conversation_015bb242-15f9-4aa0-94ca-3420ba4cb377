<template>
  <div ref="scrollContainer" class="h-full overflow-y-auto p-4 space-y-4 scroll-smooth" @scroll="handleScroll">
    <!-- Контейнер сообщений с минимальной высотой -->
    <div class="min-h-full">
      <!-- Сообщения -->
      <div v-for="message in messages" :key="message.id" :class="['flex mb-4', message.role === 'user' ? 'justify-end' : 'justify-start']">
        <div :class="['max-w-3xl rounded-lg px-4 py-3 shadow-sm', message.role === 'user' ? 'bg-blue-500 text-white' : 'bg-white border border-gray-200 text-gray-900']">
          <!-- Содержимое сообщения -->
          <div v-if="message.role === 'assistant' && message.content === '' && !message.toolCalls?.length" class="flex items-center space-x-2">
            <div class="animate-spin rounded-full h-3 w-3 border-2 border-blue-500 border-t-transparent"></div>
            <span class="text-sm text-gray-600">Агент печатает...</span>
          </div>
          <ChatMessageContent v-else :message="message" />

          <!-- Отображение файлов -->
          <div v-if="message.files && message.files.length > 0" class="mt-3 space-y-2">
            <a
              v-for="(file, index) in message.files"
              :key="index"
              :href="file.url"
              target="_blank"
              rel="noopener noreferrer"
              :class="[
                'inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                message.role === 'user' ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-green-50 hover:bg-green-100 text-green-800 border border-green-200'
              ]"
            >
              <i :class="['mr-2', file.type === 'xlsx' ? 'pi pi-file-excel' : 'pi pi-file-pdf']"></i>
              {{ file.fileName }}
            </a>
          </div>

          <!-- Действия для сообщений агента -->
          <div v-if="message.role === 'assistant'" class="mt-3 flex items-center gap-2">
            <Button
              @click="openEmailSender(message)"
              size="small"
              severity="secondary"
              outlined
              icon="pi pi-send"
              label="Отправить как email"
              v-tooltip.top="'Отправить это сообщение клиенту по email'"
            />
            <Button @click="copyMessage(message)" size="small" severity="secondary" outlined icon="pi pi-copy" v-tooltip.top="'Скопировать сообщение'" />
          </div>
        </div>

        <!-- Время сообщения -->
        <div :class="['text-xs mt-2 flex items-center justify-between', message.role === 'user' ? 'text-blue-100' : 'text-gray-400']">
          <span>{{ formatTimestamp(message.timestamp) }}</span>
          <div v-if="message.emailSent" class="flex items-center gap-1 text-green-600">
            <i class="pi pi-check-circle"></i>
            <span class="text-xs">Отправлено по email</span>
          </div>
        </div>
      </div>

      <!-- Индикатор загрузки (показываем только если нет пустого сообщения агента) -->
      <div v-if="isLoading && !hasEmptyAssistantMessage" class="flex justify-start mb-4">
        <div class="bg-white border border-gray-200 rounded-lg px-4 py-3 shadow-sm">
          <div class="flex items-center space-x-3">
            <div class="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent"></div>
            <span class="text-sm text-gray-600">Агент обрабатывает запрос...</span>
          </div>
        </div>

        <!-- Сообщение об отсутствии сообщений -->
        <div v-if="messages.length === 0 && !isLoading" class="text-center py-8">
          <div class="text-gray-400 text-sm">
            <i class="pi pi-comments text-2xl mb-2 block"></i>
            Начните диалог с агентом
          </div>
        </div>
      </div>

      <!-- Диалог отправки email -->
      <EmailSender
        v-model:visible="showEmailSender"
        :message-content="selectedMessage?.content || ''"
        :recipient-email="extractEmailFromMessages()"
        :original-message="originalUserMessage"
        @email-sent="handleEmailSent"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch, computed } from 'vue'
import { useToast } from 'primevue/usetoast'
import Button from 'primevue/button'
import ChatMessageContent from './ChatMessageContent.vue'
import EmailSender from './EmailSender.vue'
import type { Message } from '@/lib/interfaces/Agent'
import { useAgentStore } from '@/stores/agent'

const agentStore = useAgentStore()

interface Props {
  messages: Message[]
  isLoading: boolean
}
const props = defineProps<Props>()

const toast = useToast()

// Реф для контейнера прокрутки
const scrollContainer = ref<HTMLElement>()

// Состояние для email отправки
const showEmailSender = ref(false)
const selectedMessage = ref<Message | null>(null)
const originalUserMessage = ref('')

// Функция прокрутки к низу
const scrollToBottom = () => {
  if (scrollContainer.value) {
    const { scrollHeight, clientHeight } = scrollContainer.value
    //console.log(`[MessageList] Scroll info: scrollHeight=${scrollHeight}, clientHeight=${clientHeight}`)
    scrollContainer.value.scrollTop = scrollHeight
  }
}

// Проверка, находится ли пользователь внизу списка
const isAtBottom = ref(true)

// Проверяем, есть ли пустое сообщение агента (для стриминга)
const hasEmptyAssistantMessage = computed(() => {
  return props.messages.some((msg) => msg.role === 'assistant' && msg.content === '')
})

// Обработчик прокрутки
const handleScroll = () => {
  if (scrollContainer.value) {
    const { scrollTop, scrollHeight, clientHeight } = scrollContainer.value
    // Допуск в 10px, чтобы считать, что мы "внизу"
    isAtBottom.value = scrollTop + clientHeight >= scrollHeight - 10
  }
}

// Улучшенный watch для автоскролла
watch(
  () => props.messages,
  (newMessages, oldMessages) => {
    // Скроллим, только если пользователь внизу
    if (isAtBottom.value) {
      nextTick(() => {
        scrollToBottom()
      })
    }
  },
  { deep: true } // Используем deep watch для отслеживания изменений внутри сообщений (toolCalls)
)

// Первичная прокрутка при загрузке
watch(
  () => props.isLoading,
  (loading) => {
    if (!loading && props.messages.length > 0) {
      nextTick(() => {
        scrollToBottom()
      })
    }
  },
  { immediate: true }
)

// Открытие диалога отправки email
const openEmailSender = (message: Message) => {
  selectedMessage.value = message
  originalUserMessage.value = extractOriginalUserMessage(message)
  showEmailSender.value = true
}

// Отправка email
const handleEmailSent = async (emailData: { to: string; subject: string; message: string }) => {
  if (selectedMessage.value) {
    const message = props.messages.find((m) => m.id === selectedMessage.value!.id)
    if (message) {
      message.emailSent = true
      message.emailSentAt = new Date()
    }

    // Сохраняем метку об отправке email в Mastra memory
    try {
      await agentStore._saveServiceMessage(`[СЛУЖЕБНАЯ ИНФОРМАЦИЯ] Email отправлен клиенту ${emailData.to}. Тема: "${emailData.subject}". Дата отправки: ${new Date().toLocaleString('ru-RU')}`, {
        recipientEmail: emailData.to,
        subject: emailData.subject,
        sentAt: new Date().toISOString(),
        messageId: selectedMessage.value.id
      })
    } catch (error) {
      console.error('Ошибка сохранения метки об отправке email в memory:', error)
      // Не показываем ошибку пользователю, так как это служебная функция
    }

    toast.add({
      severity: 'success',
      summary: 'Письмо отправлено',
      detail: 'Email успешно добавлен в очередь на отправку.',
      life: 3000
    })
  }
}

// Форматирование времени
const formatTimestamp = (timestamp: Date): string => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

// Копирование сообщения в буфер обмена
const copyMessage = async (message: Message) => {
  try {
    await navigator.clipboard.writeText(message.content)
    toast.add({
      severity: 'success',
      summary: 'Скопировано',
      detail: 'Сообщение скопировано в буфер обмена',
      life: 2000
    })
  } catch (error) {
    console.error('Ошибка копирования:', error)
    toast.add({
      severity: 'error',
      summary: 'Ошибка',
      detail: 'Не удалось скопировать сообщение',
      life: 3000
    })
  }
}

// Извлечение email из сообщений
const extractEmailFromMessages = (): string => {
  // Ищем email в сообщениях пользователя
  for (const message of props.messages) {
    if (message.role === 'user') {
      const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g
      const emails = message.content.match(emailRegex)
      if (emails && emails.length > 0) {
        return emails[0]
      }
    }
  }
  return ''
}

// Поиск оригинального сообщения пользователя для email
const extractOriginalUserMessage = (message: Message): string => {
  const index = props.messages.findIndex((m) => m.id === message.id)
  if (index > 0) {
    for (let i = index - 1; i >= 0; i--) {
      if (props.messages[i].role === 'user') {
        return props.messages[i].content
      }
    }
  }
  return ''
}

// Форматирование названий инструментов
const formatToolName = (toolName: string): string => {
  const toolNames: Record<string, string> = {
    searchTool: 'Поиск товаров',
    findOrgTool: 'Поиск реквизитов организации',
    generateDocTool: 'Генерация документов'
  }
  return toolNames[toolName] || toolName
}
</script>

<style scoped>
/* Стили для скроллбара */
.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 4px;
  margin: 4px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  border: 2px solid #f8fafc;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Плавная прокрутка */
.scroll-smooth {
  scroll-behavior: smooth;
}

/* Стили для кода */
pre {
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Дополнительные стили для Markdown в сообщениях агента */
.bg-white :deep(.markdown-content) {
  color: #374151; /* text-gray-700 */
}

.bg-white :deep(.markdown-link) {
  color: #2563eb; /* text-blue-600 */
}

.bg-white :deep(.markdown-link:hover) {
  color: #1d4ed8; /* text-blue-700 */
}

.bg-white :deep(.markdown-heading) {
  color: #111827; /* text-gray-900 */
}

.bg-white :deep(.markdown-inline-code) {
  background-color: #f3f4f6; /* bg-gray-100 */
  color: #374151; /* text-gray-700 */
}

.bg-white :deep(.markdown-code-block) {
  background-color: #f9fafb; /* bg-gray-50 */
  border-color: #e5e7eb; /* border-gray-200 */
}

.bg-white :deep(.markdown-blockquote) {
  border-left-color: #d1d5db; /* border-gray-300 */
  color: #6b7280; /* text-gray-500 */
}

/* Убираем лишние отступы для первого и последнего элемента */
.bg-white :deep(.markdown-content > *:first-child) {
  margin-top: 0;
}

.bg-white :deep(.markdown-content > *:last-child) {
  margin-bottom: 0;
}
</style>
