<script setup lang="ts">
import { defineProps, nextTick, onBeforeMount, onMounted, ref, watch } from 'vue'
import { api } from '@/lib/_api'
import ProductCard from '@/components/product/ProductCard.vue'
import { useRoute, useRouter } from 'vue-router'
import ProgressSpinner from 'primevue/progressspinner'
import { useQuery } from '@tanstack/vue-query'

// const product = ref({})
const rmt = ref(true)

const router = useRouter()
const route = useRoute()

let qs

const title = ref('Редактировать товар:')

onMounted(() => {
  qs = new URLSearchParams(route.query)

  if (qs.has('editormode')) {
    //console.log('editormode!')
    window.document.querySelector('header').remove()
  }
})

const {
  isFetching,
  isSuccess,
  isError,
  data: product,
  error,
  refetch
} = useQuery({
  queryKey: ['productpage'],
  queryFn: async () => await loadProduct(),
  refetchOnWindowFocus: false,
  refetchOnMount: true
})

const loadProduct = async () => {
  const { body } = await api('/cpan/product/' + route.params.id)
  // product.value = body

  return body
}

const deleteProductHandler = async () => {
  if (qs?.has?.('editormode')) {
    rmt.value = false
    return false
  } else {
    router.push('/goods')
  }
}

const reload = async () => {
  // await loadProduct()
  refetch()
  rmt.value = false
  await nextTick()
  rmt.value = true
}

// watch(product, () => //console.log('watcher product: ', product.value))

onMounted(async () => {
  try {
    document.title = 'Редактировать товар'
  } catch (error) {}
})
</script>

<template>
  <div class="bg-white dark:bg-zinc-900 p-2 rounded-lg -m-2">
    <div v-if="product && rmt && !isFetching">
      <ProductCard @onDelete="deleteProductHandler" @delete="deleteProductHandler" @update="reload" :productData="product" />
    </div>
    <div v-else>
      <div class="flex items-center justify-center h-screen p-5">
        <ProgressSpinner />
      </div>
    </div>
  </div>
</template>
