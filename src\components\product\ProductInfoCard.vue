<script setup lang="ts">
import { trnColumns } from '@/lib/trnColumns'
// import dayjs from 'dayjs';
import Image from 'primevue/image'
import { ref } from 'vue'

const props = defineProps({
  productData: Object,
  showLink: Boolean
})

//console.log('ProductInfoCard props:', props)

const fieldList = {
  withoutAutoCompeteFields: ['prod_size', 'prod_supplier', 'prod_sku', 'prod_analogsku', 'prod_group', 'prod_cell'],
  simpleTextFields: ['prod_purpose', 'prod_uses', 'prod_sku', 'prod_analogsku', 'prod_group', 'prod_size', 'prod_supplier', 'prod_type', 'prod_manuf', 'prod_year', 'prod_model', 'prod_material', 'prod_cell'],
  numberFields: ['prod_purchasing', 'prod_coeff', 'prod_count', 'prod_minalert', 'prod_weight'],
  editorFields: ['prod_note', 'prod_composition', 'prod_secret']
}

// const productTextFields = ref([...fieldList.numberFields, ...fieldList.simpleTextFields, ...fieldList.withoutAutoCompeteFields, ...fieldList.editorFields])
</script>

<template>
  <div v-if="props.productData">
    <div class="flex space-x-5">
      <div class="flex flex-col w-3/5">
        <div :key="field" v-for="field in [...fieldList.simpleTextFields, ...fieldList.withoutAutoCompeteFields, ...fieldList.editorFields]">
          <div class="flex space-x-3 items-center">
            <div class="font-semibold">{{ trnColumns(field) }}</div>
            :
            <div v-if="props.productData?.[field]">{{ props.productData[field] }}</div>
          </div>
        </div>
      </div>
      <div class="flex flex-col">
        <div>
          <div class="flex space-x-3 items-center">
            <div class="font-semibold">Цена:</div>
            <div>{{ props.productData['prod_price'] }}</div>
          </div>
          <div class="flex space-x-3 items-center">
            <div class="font-semibold">Цена опт:</div>
            <div>{{ props.productData['whosaleprice'] || props.productData['prod_discount'] }}</div>
          </div>
        </div>
        <div :key="field" v-for="field in fieldList.numberFields">
          <div class="flex space-x-3 items-center">
            <div class="font-semibold">{{ trnColumns(field) }}</div>
            :
            <div>{{ props.productData[field] }}</div>
          </div>
        </div>
        <!-- <div>
          <Image imageClass="max-w-64" preview :src="`https://mirsalnikov.ru/data/rti/${props.productData.prod_img}.jpg`" :alt="props.productData.prod_img">
            <template #indicator> Увеличить </template>
          </Image>
        </div> -->
      </div>
    </div>
    <div v-if="showLink" class="flex justify-end">
      <div>
        <a target="_blank" :href="'/products/' + props.productData.prod_id" class="text-slate-500 dark:text-gray-300"> Перейти в карточку товара </a>
      </div>
    </div>
  </div>
  <div v-else>Ошибка</div>
</template>
