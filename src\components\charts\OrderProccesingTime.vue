<template>
  <div>
    <h1 class="text-slate-500 dark:text-gray-300 font-semibold">Среднее время на заказ</h1>
    <div class="flex justify-center">
      <Chart :canvasProps="{ height: 280 }" ref="chart" type="line" :data="chartData" :options="options" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Chart from 'primevue/chart'

const chart = ref<Chart | null>(null)

const props = defineProps({
  data: {
    type: Array as () => { month: string; avg_time_spent: number }[],
    required: true
  }
})

// const data = [
//     { month: 'Январь', avg_time_spent: 181559.3184 },
//     { month: 'Февраль', avg_time_spent: 108347.974 },
//     { month: 'Март', avg_time_spent: 60715.8853 }
// ]

const options = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    tooltip: {
      mode: 'index',
      intersect: false
    },
    legend: {
      labels: {
        color: '#495057'
      }
    }
  },
  scales: {
    x: {
      stacked: true,
      ticks: {
        color: '#495057'
      },
      grid: {
        color: '#ebedef'
      }
    },
    y: {
      stacked: true,
      ticks: {
        color: '#495057'
      },
      grid: {
        color: '#ebedef'
      }
    }
  }
}

const chartData = {
  labels: props.data.map((item) => item.month),
  datasets: [
    {
      type: 'bar',
      label: 'Часов до статуса "Завершен"',
      backgroundColor: '#475569',
      data: props.data.map((item) => Math.ceil(item.avg_time_spent / 3600))
    }
  ]
}

onMounted(() => {})
</script>
