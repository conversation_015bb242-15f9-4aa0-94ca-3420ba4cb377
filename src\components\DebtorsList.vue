<script lang="ts" setup>
import { api } from '@/lib/_api'
import { useQuery } from '@tanstack/vue-query'
import ScrollPanel from 'primevue/scrollpanel'

const { isLoading, isError, data, error } = useQuery({
  queryKey: ['debtors'],
  queryFn: async () => await loadDebtors(),
  refetchOnMount: false,
  refetchOnWindowFocus: true,
  refetchOnReconnect: true
})

const loadDebtors = async () => {
  const res = await api('/cpan/clients/debtors')
  return res?.body
}
</script>

<template>
  <div>
    <span class="text-slate-600 dark:text-zinc-100 font-semibold">Должники</span>

    <ScrollPanel style="width: 100%; height: 550px" class="custom">
      <div class="mt-3 text-slate-500 dark:text-gray-300">
        <div class="mt-2" v-for="(debtor, index) in data" :key="index">
          <router-link class="font-semibold text-sm" :to="'/clients/' + debtor.order_client">
            {{ index + 1 }}. {{ debtor.client_name }} <span class="text-sm text-slate-600 dark:text-zinc-100">({{ debtor.client_mail }})</span>
            -
            <span class="font-semibold text-slate-600 dark:text-zinc-100">{{ Number(debtor?.sum).toLocaleString() || '...' }} руб.</span>
          </router-link>
        </div>
      </div>
    </ScrollPanel>
  </div>
</template>
