<script setup lang="ts">
import { trnColumns } from '@/lib/trnColumns'
import { api } from '@/lib/_api'

import Editor from 'primevue/editor'
import Inplace from 'primevue/inplace'
import Image from 'primevue/image'
import AutoComplete from 'primevue/autocomplete'
import Toolbar from 'primevue/toolbar'
import ProgressSpinner from 'primevue/progressspinner'
import Chips from 'primevue/chips'
import Checkbox from 'primevue/checkbox'
import Galleria from 'primevue/galleria'
import FileUpload from 'primevue/fileupload'
import TieredMenu from 'primevue/tieredmenu'
import Dialog from 'primevue/dialog'
import OverlayPanel from 'primevue/overlaypanel'
import PickList from 'primevue/picklist'
import InputNumber from 'primevue/inputnumber'
import ScrollPanel from 'primevue/scrollpanel'
import Divider from 'primevue/divider'
import ProductMapArea from '@/components/product/ProductMapArea.vue'
import ConfirmDialog from 'primevue/confirmdialog'
import Textarea from 'primevue/textarea'
import ImageEditor from '@/components/imgeditor/ImageEditor.vue'
// import ImageEditor from "./components/ImageEditor.vue"

import { dataUrlToFile } from '@/lib/base64toFile'
import { nextTick, onBeforeUnmount, onMounted, onUnmounted, reactive } from 'vue'
import { ref } from 'vue'
import { useToast } from 'primevue/usetoast'
import { useConfirm } from 'primevue/useconfirm'
import { watch } from 'vue'
import { computed } from 'vue'
import { onBeforeMount } from 'vue'
import { useUserStore } from '@/stores/user'
import { useSettingsStore } from '@/stores/apisettings'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'
import FindProduct from './FindProduct.vue'

import { removeDuplicatesAndTrim } from '@/lib/removeDuplicats'
import CategorySelect from './CategorySelect.vue'
import { useAppStore } from '@/stores/app'
import Timeline from 'primevue/timeline'
import dayjs from 'dayjs'

const { userData } = useUserStore()
const { settingsData } = useSettingsStore()
const { appStoreData } = useAppStore()

const props = defineProps({
  productData: Object,
  onDelete: Object
})

const emit = defineEmits(['update', 'duplicate', 'delete'])

const toast = useToast()
const confirm = useConfirm()

const menu = ref()
const stat = ref()
const duplicatsPanel = ref()

const showAnyProduct = ref(false)
const modalProduct = ref({})

const mounted = ref(false)

const printStickerVisible = ref(false)

const state = reactive({
  imageEditorVisible: false,
  product: {
    prod_analogs: []
  },
  orderList: [],
  specSum: null,
  specSumLoading: false,
  snapshots: [],
  inplaces: [],
  displayChangeList: false,
  updateGroupModal: false,
  displaySchemaForm: false,
  displayOrderList: false,
  showRK: false,
  showRKhighlight: false,
  RKfilterModel: undefined,
  filteredProds: [],
  rkNewItems: [],
  images: [],
  testChip: '',
  addStatForm: {
    qty: 1,
    notice: ''
  },
  statLoading: false,
  salesLoading: false,
  statCount: '',
  salesCount: '',
  isLoadingSave: false,
  isLoadingDel: false,
  selectedFieldsForUpdate: ['prod_cell', 'prod_weight', 'prod_count', 'prod_sku', 'prod_analogsku', 'prod_sku', 'prod_analogs', 'prod_price', 'prod_discount'],
  acData: {},
  showFr: false,
  currency: 0,
  coeff: 0,
  duplicats: [],
  mergeItems: [],
  textareaFields: ['prod_purpose', 'prod_uses'],
  withoutAutoCompeteFields: ['prod_size', 'prod_supplier', 'prod_sku', 'prod_analogsku', 'prod_group', 'prod_cell'],
  simpleTextFields: [
    // 'prod_purpose',
    // 'prod_uses',
    'prod_sku',
    'prod_analogsku',
    'prod_group',
    'prod_size',
    'prod_supplier',
    'prod_type',
    'prod_manuf',
    'prod_year',
    'prod_model',
    'prod_material',
    'prod_cell'
  ],
  numberFields: ['prod_discount', 'prod_purchasing', 'prod_coeff', 'prod_count', 'prod_minalert', 'prod_weight'],
  nfp: {
    prod_count: 1,
    prod_price: 0.1,
    prod_weight: 0.1,
    prod_coeff: 0.1,
    prod_purchasing: 0.1
  },
  fields: [],
  editorFields: ['prod_note', 'prod_composition', 'prod_secret'],
  menuItems: [
    {
      label: 'Дублировать товар',
      icon: 'pi pi-clone',
      method: duplicate
    },
    {
      label: 'Показать дубликаты',
      icon: 'pi pi-tags',
      method: toggleDuplicats
    },
    {
      label: props.productData?.schema ? 'Ред. схему' : 'Добавить схему',
      icon: props.productData?.schema ? 'pi pi-file-edit' : 'pi pi-plus',
      method: () => (state.displaySchemaForm = true)
    },
    {
      label: 'Снять блок',
      icon: 'pi pi-times',
      method: () => leaving('x')
    },
    {
      label: 'Создать РК',
      icon: 'pi pi-th-large',
      method: () => {
        state.showRK = true
        const rkFormRef = document.querySelector('.showRKform')
        rkFormRef?.scrollIntoView()
        state.showRKhighlight = true
        setTimeout(() => {
          state.showRKhighlight = false
        }, 1500)
      }
    },
    {
      label: 'Удалить',
      icon: 'pi pi-trash',
      method: deleteProduct
    },
    {
      label: 'Добавить к спросу',
      icon: 'pi pi-plus-circle',
      method: () => (state.displayAddStatForm = true)
    },
    {
      label: 'Показать заказы',
      icon: 'pi pi-table',
      method: showOrderList
    },
    {
      label: 'История изменений',
      icon: 'pi pi-arrow-right-arrow-left',
      method: showChangeList
    },
    {
      label: 'Наклейка',
      icon: 'pi pi-print',
      method: () => {
        printElement('.sticker-container')
      }
    }
  ],
  displayAddStatForm: false,
  duplicatsLoading: false,
  newImgFile: undefined
})

const allFields = computed(() => [...state.editorFields, ...state.numberFields, ...state.simpleTextFields, ...state.textareaFields, ...state.withoutAutoCompeteFields])

onBeforeMount(async () => {
  if (props.productData?.prod_id) {
    state.product = { ...props.productData }
  }

  window.onbeforeunload = () => {
    leaving(false)
    return 'Do you really want to close?'
  }

  if (state.product.prod_id) {
    const { body: _lock } = await api(`/service/lock/?entity=products&entity_id=${state.product.prod_id}&action=lock`)
    state.product.lock = _lock

    if (_lock?.id && _lock.user_id !== userData.value.user_id) {
      confirm.require({
        message: `Товар открыт на редактирование: ${_lock.user_name} в ${_lock.created_at}`,
        header: 'Внимание!',
        icon: 'pi pi-exclamation-triangle',
        acceptIcon: 'pi pi-times',
        acceptLabel: 'Снять блок',
        rejectLabel: 'Продолжить просмотр',
        accept: () => {
          leaving('x')
        },
        reject: () => {},
        onHide: () => {}
      })
    }
  }

  if (state.product?.rk?.length) {
    state.showRK = true

    //console.log('this.product.rk:', state.product?.rk)
    // this.product.rk.map(item => )
    const lowest = state.product.rk.reduce((prev, cur) => (cur.product.prod_count / cur.qty < prev.product.prod_count / prev.qty ? cur : prev))

    if (lowest) {
      lowest.min = true
    }
  }

  if (!state.product.prod_coeff) {
    state.coeff = settingsData.value?.data?.appSetings?.settings?.['products.defaultCoeff']
  } else {
    state.coeff = state.product.prod_coeff
  }

  state.currency = settingsData.value?.data?.appSetings?.settings?.['products.priceCurrency'] || 0

  if (!state.product.prod_purchasing) {
    state.product.prod_purchasing = state.product.prod_price / state.coeff / state.currency
  }

  Object.keys(state.product).map((key) => state.fields.push({ key, type: isNaN(state.product[key]) ? 'text' : 'number' }))
  state.fields = state.fields.filter((i) => !state.editorFields.includes(i.key))

  state.images = [
    {
      src: `https://mirsalnikov.ru/data/rti/${state.product.prod_img}.jpg`,
      alt: 'Description for Image 12',
      title: 'Title 12'
    },
    {
      src: `https://rumisota.eu/data/rumi/${state.product.prod_img_rumi}.jpg`,
      alt: 'Description for Image 12',
      title: 'Title 12'
    }
  ]

  if (!state.product.prod_id) {
    // this.product.prod_analogs = []
  } else {
    prepareAnalogs()
  }

  getStats()
})

onUnmounted(async () => {
  // await leaving(false)
  // alert('unmounted!')
})

onBeforeUnmount(async () => {
  await leaving(false)
})

watch(
  () => state.product.prod_analogsku,
  (newValue, oldValue) => {
    if (newValue && oldValue && newValue !== oldValue) {
      let sku = String(newValue).trim()
      if (sku?.length > 3 || (!state.product.prod_id && sku?.length > 3)) {
        try {
          api('/products/findexist?oem=' + sku).then((res) => {
            if (res.ok) {
              confirm.require({
                message: 'Товар с таким ОЕМ уже существует.',
                header: 'Внимание!',
                icon: 'pi pi-exclamation-triangle',
                acceptLabel: 'Продолжить'
              })
            }
          })
        } catch (error) {}
      }
    }
  },
  { deep: true }
)

const whosalePrice = computed(() => {
  try {
    return state.product.prod_price - (state.product.prod_price / 100) * state.product.prod_discount
  } catch (error) {
    return state.product.prod_price - (state.product.prod_price / 100) * state.product.prod_discount
  }
})

function handleFileUpload(event) {
  const reader = new FileReader()
  const file = event.files[0]

  reader.onloadend = async () => {
    // imageSrc.value = reader.result
    state.newImgFile = file
    state.imageEditorVisible = true
  }

  reader.readAsDataURL(file)
}

async function saveEditorHandler(base64String: string) {
  //   //console.log('🚀 ~ file: ProductCard.vue:717 ~ saveEditorHandler ~ base64String:', base64String)
  getUploadImgURL()

  //   const binaryData = atob(base64String)
  //   const file = new Blob([binaryData], {type: 'application/octet-stream'})

  const filename = Math.random().toString(36).substring(2) + '.jpg'
  const file = await dataUrlToFile(base64String, filename)
  //   //console.log("🚀 ~ file: ProductCard.vue:730 ~ saveEditorHandler ~ file:", file)

  const formData = new FormData()
  formData.append('mainImg[]', file, filename)

  await fetch(getUploadImgURL(), {
    method: 'POST',
    body: formData
  })

  uploadHandler()
}

function getAnalogsString() {
  try {
    return state.product.prod_analogs.join(', ')
  } catch (error) {
    //console.log('getAnalogsString error: ', error)
    return state.product.prod_analogs
  }
}

async function showChangeList() {
  try {
    const res = await api('/cpan/products/snapshots/?prod_id=' + state.product.prod_id)
    //console.log('🚀 ~ file: ProductCard.vue:339 ~ showChangeList ~ res:', res)

    if (res.ok) {
      state.snapshots = res.body
      state.displayChangeList = true
    }
  } catch (error) {
    console.error(error)
  }
}

async function showOrderList() {
  try {
    const res = await api('/cpan/products/orders/' + state.product.prod_id)
    if (res.ok) {
      state.orderList = res.body
      state.displayOrderList = true
    }
  } catch (error) {
    console.error(error)
  }
}

async function deleteSchemaHandler() {
  const res = await api('/cpan/products/schema/delete/' + state.product.prod_id)
  if (res.ok) {
    toast.add({ severity: 'info', summary: 'Схема удалена', life: 3000 })
    state.displaySchemaForm = false
    emit('update')
  }
}
async function createSchemaHandler(data: any) {
  // //console.log("🚀 ~ file: ProductCard.vue:634 ~ createSchemaHandler ~ data", data)

  const res = await api('/cpan/products/schema', {
    method: 'POST',
    data: {
      id: state.product.prod_id,
      body: data
    }
  })

  if (res.ok) {
    toast.add({ severity: 'info', summary: 'Схема создана', life: 3000 })
    state.displaySchemaForm = false
    emit('update')
  } else {
    toast.add({ severity: 'error', summary: 'Ошибка', life: 3000 })
  }
}

function uploadHandler() {
  toast.add({ severity: 'info', summary: 'Изображения загружены', life: 3000 })
  emit('update')
}
async function deleteImage(item: any) {
  const { name, path } = item
  const res = await api(`/service/files/delete?name=${name}&path=${encodeURIComponent(path)}&target=${props.productData?.prod_id}`)
  if (res.ok) {
    toast.add({ severity: 'info', summary: 'Изображение удалено', life: 3000 })
    emit('update')
  }
  // if (res.ok) // toast
}
function returnBasePath() {
  // return (import.meta.env.DEV ? import.meta.env['VITE_API_URL_DEV'] : import.meta.env['VITE_API_URL']) + '/data'
  return import.meta.env['VITE_API_URL'] + '/data'
}

function getUploadImgURL() {
  const base = import.meta.env.DEV ? import.meta.env['VITE_API_URL_DEV'] : import.meta.env['VITE_API_URL']
  return base + `/service/files/upload/images?target=${props.productData?.prod_id}`
}

function duplicate() {
  props.productData?.prod_purpose && (props.productData.prod_purpose = 'Копия ' + props.productData.prod_purpose)
  state.product?.prod_purpose && (state.product.prod_purpose = 'Копия ' + state.product.prod_purpose)
  emit('duplicate')
  delete props.productData?.prod_id
  delete state.product?.prod_id
}

function prepareAnalogs() {
  if (state.product.prod_analogs && typeof state.product.prod_analogs == 'string') {
    state.product.prod_analogs = state.product.prod_analogs?.split(',') //.filter((i) => i)
  }
}

function RKinputChangeHandler({ value }, id) {
  if (value < 1) {
    state.product.rk = state.product.rk.filter((i) => i.id != id)
  }
}
async function findRKitemHandler({ query: value }) {
  if (String(value).length > 3) {
    // const { body } = await api('/cpan/products/?' + 'searchvalue=' + value)
    const { body } = await api('/search/fast/' + value)
    state.filteredProds = body.products //.data
  } else {
    state.filteredProds = []
  }
}

function addToRK({ value }) {
  //console.log('🚀 ~ file: ProductCard.vue:457 ~ addToRK ~ value:', value)
  // state.product?.rk?.unshift({
  //   id: value.prod_id,
  //   qty: 1,
  //   product: value,
  //   new: true
  // })

  state.product.rk = [
    {
      id: value.prod_id,
      qty: 1,
      product: value,
      new: true
    },
    ...(state.product.rk ?? [])
  ]

  state.RKfilterModel = undefined

  // //console.log("🚀 ~ file: ProductCard.vue:459 ~ addToRK ~ state.product?.rk?:", state.product?.rk)
}

function toggleStat(event) {
  stat.value.toggle(event)
}

function toggleDuplicats(event) {
  duplicatsPanel.value.toggle(event)
}

function initMerge() {
  const mergeFields = ['prod_composition', 'prod_model', 'prod_note', 'prod_secret', 'prod_uses', 'prod_cell']
  const m_prods = state.duplicats[1] //this.duplicats.filter(x => this.mergeItems.includes(String(x.prod_id)))

  let mainProductAnalogsArr = Array.isArray(state.product.prod_analogs) ? state.product.prod_analogs : state.product.prod_analogs?.split(',')?.filter((x) => x) || []

  const suppliers = [state.product.prod_manuf]
  let purposes =
    state.product.prod_purpose
      ?.split(',')
      ?.map((i) => i.trim())
      .filter((i) => i) || [] //[state.product.prod_purpose]
  purposes = Array.from(new Set(purposes.map((i) => i.trim()).filter((i) => i)))

  m_prods.map((prod) => {
    let currentProdAnalogsArr =
      prod.prod_analogs
        ?.split(',')
        ?.map((i) => i.trim())
        .filter((x) => x) || []

    Object.keys(prod).map((key) => {
      if (mergeFields.includes(key) && prod[key] && prod[key] != state.product[key]) {
        state.product[key] += ', ' + prod[key]
      }
    })

    mainProductAnalogsArr.push(...[prod.prod_analogsku, prod.prod_sku], ...currentProdAnalogsArr)

    suppliers.push(prod.prod_manuf)
    purposes.push(prod.prod_purpose)
  })

  if (purposes.length) {
    state.product.prod_purpose = [...new Set(purposes)].join()
  }

  if (suppliers.length) {
    state.product.prod_supplier = [...new Set(suppliers)].join()
  }

  if (mainProductAnalogsArr.length) {
    state.product.prod_analogs = [...new Set(mainProductAnalogsArr)] //.join()
  }

  if (!state.product.prod_composition) {
    state.product.prod_composition = ''
  }

  if (m_prods.length) {
    state.product.prod_composition += `(Склеено с ${m_prods.map((i) => i.prod_id)})`
    state.mergeItems = [...m_prods]
  }

  if (state.product.prod_uses) {
    try {
      state.product.prod_uses = [...new Set(state.product.prod_uses.split(','))].join()
    } catch (error) {}
  }

  duplicatsPanel.value.hide()
  state.duplicats[1] = []
}

async function getDuplicats() {
  if (state.duplicats) {
    // return false
  }

  state.duplicatsLoading = true
  let _url = '/products/duplicats/'

  const { body } = await api(_url + state.product.prod_id)
  // this.duplicats = [body, this.mergeItems]
  state.duplicats = [body || [], []]
  state.duplicatsLoading = false
}

function showMergeProductHandler(product) {
  modalProduct.value = product
  showAnyProduct.value = true
}

function addToDuplicats(data) {
  // //console.log('addToDuplicats:', data)
  state.duplicats[0].unshift(data)
}

async function leaving(x: any) {
  await api(`/service/lock/?entity=products&entity_id=${state?.product?.prod_id}&action=${x ? 'forceunlock' : 'unlock'}`)
}

async function removeStatItem(event, itemId) {
  confirm.require({
    target: event.currentTarget,
    message: ``,
    header: 'Подтвердить удаление',
    icon: 'pi pi-exclamation-triangle',
    acceptLabel: 'Удалить',
    acceptClass: 'bg-red-400',
    rejectLabel: 'Отмена',
    accept: async () => {
      try {
        const res = await api('/cpan/statistics/remove?id=' + itemId)

        if (res.ok || res.isOk) {
          toast.add({ severity: 'success', summary: 'Спрос удален', detail: '', life: 3000 })
          emit('update')
        } else {
          toast.add({ severity: 'error', summary: `Ошибка cервера`, detail: `${res.status} ${res.statusText}`, life: 6000 })
        }
      } catch (error) {
        toast.add({ severity: 'error', summary: `Ошибка`, detail: error, life: 6000 })
      }
    },
    reject: () => {
      //callback to execute when user rejects the action
    }
  })
}

async function getSpecSum() {
  state.specSumLoading = true
  try {
    const res = await api('/service/specsum/?oem=' + state.product.prod_analogsku)

    if (res.ok) {
      state.specSum = res.body
    } else {
      toast.add({ severity: 'error', summary: `Ошибка cервера`, detail: `${res.status} ${res.statusText}`, life: 6000 })
    }
  } catch (error) {
    toast.add({ severity: 'error', summary: 'Ошибка получения данных вывоза', detail: error, life: 6000 })
  }
  state.specSumLoading = false
}
async function addStat(e) {
  try {
    const res = await api(`/statistics/w/${state.product.prod_analogsku}?count=${state.addStatForm.qty}&info=${state.addStatForm.notice}&manual=1`)

    if (res.ok) {
      toast.add({ severity: 'success', summary: 'Спрос обновлен', detail: '', life: 3000 })

      //state.product = res.body.product
      emit('update')
      await getStats()
      state.displayAddStatForm = false
    } else toast.add({ severity: 'error', summary: `Ошибка cервера`, detail: `${res.status} ${res.statusText}`, life: 6000 })
  } catch (error) {
    toast.add({ severity: 'error', summary: 'Ошибка cервера', detail: error, life: 6000 })
  }
}

function toggleMenu(e) {
  menu.value.toggle(e)
}
async function getStats() {
  state.statLoading = true
  state.salesLoading = true

  const statData = await api(`/cpan/statistics/byoem/${encodeURIComponent(state.product.prod_analogsku)}?sales=true&stat=true`)

  if (statData.ok) {
    const { stat, sales } = statData.body

    state.statCount = stat
    state.salesCount = sales?.sales

    state.statLoading = false
    state.salesLoading = false
  }
}

function deleteProduct(event) {
  confirm.require({
    target: event.currentTarget,
    message: `Удалить ${state.product.prod_purpose} ${state.product.prod_analogsku} ?`,
    header: 'Подтвердить удаление',
    icon: 'pi pi-exclamation-triangle',
    acceptLabel: 'Удалить',
    acceptClass: 'bg-red-400',
    rejectLabel: 'Отмена',
    accept: async () => {
      try {
        const res = await api('/cpan/products/delete/' + state.product.prod_id)

        if (res.ok || res.isOk) {
          toast.add({ severity: 'success', summary: 'Товар удален', detail: '', life: 3000 })
          props.onDelete && props.onDelete()
          emit('delete')
        } else {
          toast.add({ severity: 'error', summary: `Ошибка cервера`, detail: `${res.status} ${res.statusText}`, life: 6000 })
        }
      } catch (error) {
        toast.add({ severity: 'error', summary: `Ошибка`, detail: error, life: 6000 })
      }
    },
    reject: () => {
      //callback to execute when user rejects the action
    }
  })
}
async function save(event) {
  // state.updateGroupModal = true

  const _initSave = async ({ updateGroup = false }) => {
    const rmFields = ['buy_limit', 'rk', 'images']

    const payloads = {
      prod_id: state.product.prod_id,
      fields: { ...state.product },
      updateGroupFields: updateGroup ? state.selectedFieldsForUpdate : undefined
    }

    if (state.product?.rk?.length) {
      payloads.fields.prod_rk = state.product.rk.map((item) => item.id + '*' + item.qty).join(',')
      if (!state.product.prod_rk) {
        payloads.fields.prod_composition += ', старое наличие: ' + state.product.prod_count
      }
    } else {
      payloads.fields.prod_rk = ''
    }

    rmFields.map((key) => delete payloads.fields[key])

    toast.add({ severity: 'info', summary: '', detail: 'Сохранение изменений...', life: 3000 })
    try {
      const res = await api('/cpan/products/update/', { data: payloads, method: 'POST' })

      if (res.ok) {
        toast.add({ severity: 'success', summary: 'Данные обновлены', detail: '', life: 3000 })
        // state.product = res.body.product

        emit('update')

        if (state.mergeItems?.length) {
          toast.add({ severity: 'info', summary: '', detail: 'Удаление дубликатов...', life: 3000 })
          await Promise.all(
            state.mergeItems.map(async (item) => {
              await api('/cpan/products/delete/' + item.prod_id)
            })
          )
          toast.add({ severity: 'success', summary: '', detail: 'Дубликаты удалены', life: 3000 })
          state.mergeItems = []
        }
      } else {
        console.error(res)
        toast.add({ severity: 'error', summary: `Ошибка cервера: `, detail: `${res.status} ${res.statusText} ${res.message || ''}`, life: 6000 })
      }
    } catch (error) {
      console.error(error)
      toast.add({ severity: 'error', summary: 'Ошибка: ', detail: error + `${error.message || ''}`, life: 6000 })
    }
  }

  if (state.product.prod_group) {
    //console.log('start confirm')

    confirm.require({
      target: event.currentTarget,
      header: 'Обновление группового товара',
      group: 'updateGroupConfirm',
      message: 'Этот товар входит в группу, обновить другие товары группы?',
      icon: 'pi pi-question-circle',
      acceptIcon: 'pi pi-check',
      rejectLabel: 'Нет, только текущий',
      acceptLabel: 'Да, обновить всю группу',
      rejectIcon: 'pi pi-times',
      accept: async () => {
        await _initSave({ updateGroup: true })
      },
      reject: async () => {
        await _initSave({ updateGroup: false })
      }
    })
  } else {
    await _initSave({ updateGroup: false })
  }
}

function setField(event, field) {
  let { value } = event
  state.product[field] = value
  document.querySelector('.p-inplace-content').querySelector('button').click()
}

async function searchD(event, field) {
  let { query } = event
  if (query.length > 2) {
    const res = await api('/service/getfiltersdata/', {
      method: 'POST',
      data: {
        category: state.product.prod_cat,
        column: field,
        searchvalue: query
      }
    })

    if (res.ok) {
      try {
        state.acData[field] = res.body //res.body.map(i => ({label: i, value: i}))
      } catch (error) {
        console.error(error)
      }
    } else {
      state.acData[field] = []
    }
  } else {
    state.acData[field] = []
  }
}

async function printElement(elementId: string) {
  printStickerVisible.value = true

  // document.querySelector('.p-button.p-component.p-button-text.p-button-secondary.p-button-sm')?.click()
  await nextTick()
  menu.value?.hide()
  await nextTick()

  setTimeout(async () => {
    const elementToPrint = document.querySelector(elementId)
    const printContainer = document.getElementById('print-container')

    if (elementToPrint && printContainer) {
      printContainer.innerHTML = elementToPrint.innerHTML
      printStickerVisible.value = false
      await nextTick()
      window.print()
      printContainer.innerHTML = ''
    } else {
      throw new Error('print error: !elementToPrint || !printContainer')
    }
  }, 500)
}

onMounted(() => {
  mounted.value = true
  // //console.log('🚀 ~ printElement ~ menu.value:', menu.value)
  getSpecSum()
  try {
    document.title = state?.product?.prod_analogsku
  } catch (error) {}
})
</script>

<template>
  <div class="previewProductCard"></div>

  <ConfirmDialog group="updateGroupConfirm">
    <template #message="slotProps">
      <div>
        <div>
          <div class="flex mt-2">
            <i :class="slotProps.message.icon" style="font-size: 1.5rem"></i>
            <p class="pl-2">{{ slotProps.message.message }}</p>
          </div>
        </div>
        <ScrollPanel style="width: 100%; height: 450px" class="custom mt-3">
          <div class="mt-5 space-y-2">
            <div v-for="(field, key) of allFields" :key="key" class="flex items-center space-x-2">
              <Checkbox v-model="state.selectedFieldsForUpdate" :inputId="field" name="category" :value="field" />
              <label :for="key">{{ trnColumns(field) }}</label>
            </div>
          </div>
        </ScrollPanel>
      </div>
    </template>
  </ConfirmDialog>

  <span class="text-lg font-semibold text-gray-600 dark:text-gray-200"
    >{{ state.product?.prod_id ? 'Редактировать товар: ' : 'Новый товар: ' }} {{ state.product?.prod_purpose }} {{ state.product?.prod_sku }}</span
  >
  <Toolbar class="_sticky _top-16 z-10 mt-3 bcard">
    <template #start>
      <div class="flex justify-start items-center space-x-3">
        <div class="flex items-center space-x-2">
          <span>Наличие:</span>
          <span
            :class="
              'py-1 font-semibold px-2 rounded-md shadow-sm ' + (state.product.prod_count ? 'bg-green-50 dark:bg-transparent dark:text-green-500' : 'bg-red-50 dark:bg-transparent dark:text-red-500')
            "
            >{{ state.product.prod_count }}</span
          >
        </div>
        <div @click="toggleStat" class="cursor-pointer flex items-center space-x-2">
          <span>Спрос:</span>
          <ProgressSpinner v-if="state.statLoading" style="width: 30px; height: 30px" />
          <span class="bg-yellow-50 dark:bg-transparent dark:text-yellow-500 py-1 font-semibold px-2 rounded-md shadow-sm" v-else>{{ state.statCount || '--' }}</span>
        </div>
        <div class="flex items-center space-x-2">
          <span>Вывоз:</span>
          <ProgressSpinner v-if="state.specSumLoading" style="width: 30px; height: 30px" />
          <span class="bg-green-300 dark:bg-transparent dark:text-green-500 py-1 font-semibold px-2 rounded-md shadow-sm" v-else>{{ state.specSum || '--' }}</span>
        </div>
        <div class="flex items-center space-x-2">
          <span>Продано:</span>
          <ProgressSpinner v-if="state.salesLoading" style="width: 30px; height: 30px" />
          <span class="bg-yellow-100 dark:bg-transparent dark:text-orange-500 py-1 font-semibold px-2 rounded-md shadow-sm" v-else>{{ state.salesCount || '--' }}</span>
        </div>

        <div v-if="state.product.prod_group" class="flex items-center space-x-2">
          <span>Группа:</span>
          <span class="bg-yellow-100 dark:bg-yellow-400/30 py-1 font-semibold px-2 rounded-md shadow-sm">{{ state.product.prod_group }}</span>
        </div>
      </div>
    </template>

    <template #end>
      <div class="flex items-center space-x-3 sticky">
        <a v-if="state.product?.prod_id" target="_blank" :href="'/products/' + state.product.prod_id">
          <Button icon="pi pi-external-link" label="Открыть в новом окне" text class="border-none p-button-sm" />
        </a>

        <a v-if="state.product?.prod_id" target="_blank" :href="'https://mirsalnikov.ru/catalog/product/' + state.product.prod_id">
          <Button icon="pi pi-external-link" label="Открыть на сайте" text class="border-none p-button-sm" />
        </a>

        <Button type="button" label="Меню" icon="pi pi-bars" text class="p-button-secondary p-button-sm" @click="toggleMenu" />
        <TieredMenu appendTo="body" ref="menu" :model="state.menuItems" :popup="true">
          <template #item="{ item }">
            <button @click="item.method" class="py-1 px-2 mt-2">
              <i :class="item.icon"></i>
              <span class="ml-2">{{ item.label }}</span>
            </button>
          </template>
        </TieredMenu>
        <!-- <Button @click="del($event)" :loading="isLoadingDel" label="Удалить" icon="pi pi-trash" class="p-button-danger p-button-outlined  p-button-sm" iconPos="right" /> -->
        <Button @click="save($event)" :loading="state.isLoadingSave" label="Сохранить" text icon="pi pi-save" class="p-button-success p-button-sm" iconPos="right" />
      </div>
    </template>
  </Toolbar>

  <div class="mt-7">
    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
      <div class="l-col space-y-3">
        <div>
          <span class="text-gray-500 dark:text-gray-200 ml-2 text-sm font-bold">Категория</span>
          <Inplace :closable="true">
            <template #display>
              <span v-if="state.product.prod_cat">
                {{ [...(appStoreData?.categories.childCategories ?? []), ...(appStoreData?.categories.rootCategories ?? [])].find((x) => x.cat_id == state.product.prod_cat)?.cat_title }}</span
              >
              <span v-else>Редактировать. <i class="pi pi-pencil"></i></span>
            </template>
            <template #content>
              <CategorySelect :initValue="state.product.prod_cat" :multiple="false" :onChangeHandler="({ value }) => (state.product.prod_cat = value)" />
            </template>
          </Inplace>
        </div>
        <div v-for="(field, index) in state.textareaFields" :key="index">
          <span class="text-gray-500 dark:text-gray-200 ml-2 text-sm font-bold">{{ trnColumns(field) }}</span>
          <Inplace :closable="true">
            <template #display>
              <span v-if="state.product[field]">{{ state.product[field] }}</span>
              <span v-else>Редактировать. <i class="pi pi-pencil"></i></span>
            </template>
            <template #content>
              <Textarea @input="prepareAnalogs" v-model="state.product[field]" autoResize rows="3" cols="40" />
            </template>
          </Inplace>
        </div>
        <div v-for="(field, index) in state.simpleTextFields" :key="index">
          <span class="text-gray-500 dark:text-gray-200 ml-2 text-sm font-bold">{{ trnColumns(field) }}</span>
          <Inplace :closable="true">
            <template #display>
              <span v-if="state.product[field]">{{ state.product[field] }}</span>
              <span v-else>Редактировать. <i class="pi pi-pencil"></i></span>
            </template>
            <template #content>
              <InputText v-if="state.withoutAutoCompeteFields.includes(field)" v-model="state.product[field]" />
              <AutoComplete
                v-else
                :delay="300"
                _dataKey=""
                v-model="state.product[field]"
                @item-select="setField($event, field)"
                :suggestions="state.acData[field]"
                @complete="searchD($event, field)"
              />
            </template>
          </Inplace>
        </div>
        <div class="ml-2">
          <div><span class="text-gray-500 dark:text-gray-200 text-sm font-bold">Аналоги</span></div>
          <Chips style="height: auto" addOnBlur :allowDuplicate="false" separator="," v-model="state.product.prod_analogs"></Chips>
          <div class="mt-5">
            <!-- {{ getAnalogsString() }} -->
            <!-- <InputText v-model="state.product.prod_analogs" placeholder="Аналоги"  /> -->
            <Textarea @input="prepareAnalogs" v-model="state.product.prod_analogs" autoResize rows="3" cols="40" />
          </div>
        </div>
        <div>
          <span class="text-gray-500 dark:text-gray-200 ml-2 text-sm font-bold">Доп. категории</span>
          <div>
            <CategorySelect
              :initState="
                [...(appStoreData?.categories.childCategories ?? []), ...(appStoreData?.categories.rootCategories ?? [])]
                  ?.filter((i) => {
                    const morecats =
                      typeof state.product?.prod_morecats === 'string'
                        ? state.product.prod_morecats.split(',').filter((i) => i)
                        : Array.isArray(state.product?.prod_morecats)
                        ? state.product.prod_morecats
                        : []
                    return morecats.find((c) => Number(c) === Number(i.cat_id))
                  })
                  ?.map((i) => ({ label: i.cat_title, value: i.cat_id }))
              "
              :multiple="true"
              :onChangeHandler="(value) => (state.product.prod_morecats = value.map((i) => i.value).filter((i) => i))"
            />
          </div>
        </div>
      </div>

      <div class="r-col space-y-3">
        <div>
          <span class="text-gray-500 dark:text-gray-200 ml-2 text-sm font-bold">Цена</span>
          <Inplace :closable="true">
            <template #display>
              <span v-if="state.product.prod_price || state.product.prod_price === 0">
                {{ state.product.prod_price }}
                <span class="text-sm text-gray-500 dark:text-gray-200">(Опт: {{ whosalePrice }})</span>
              </span>
              <span class="text-sm" v-else>Редактировать. <i class="pi pi-pencil"></i></span>
            </template>
            <template #content>
              <InputText step="0.1" type="number" v-model="state.product.prod_price" />
            </template>
          </Inplace>
          <!-- <div class="/bg-gray-100 /shadow /rounded -ml-2 mt-2">
            <span v-if="state.product.prod_purchasing" @click="state.showFr = !state.showFr" class="cursor-pointer text-gray-500 dark:text-gray-200 text-sm ml-3 bg-yellow-100 p-1 rounded shadow-sm">
              По формуле:
              <b>{{ (state.product.prod_purchasing * state.coeff * state.currency).toFixed(2) }}</b>
            </span>
            <span v-if="state.product.prod_coeff" class="text-gray-500 dark:text-gray-200 text-sm ml-3 bg-yellow-50 p-1 rounded shadow-sm">
              По коэфф.товара:
              <b>{{ (state.product.prod_purchasing * state.product.prod_coeff * state.currency).toFixed(2) }}</b>
            </span>
          </div>
          <div v-show="state.showFr" class="mt-3 text-sm font-bold">
            Формула:
            <span class="text-gray-500 dark:text-gray-200 bg-gray-100 p-3 rounded shadow">
              закуп <span class="text-xs" v-if="state.product.prod_purchasing">({{ state.product.prod_purchasing }})</span> *
              <InputText class="p-inputtext-sm text-sm w-14" step="0.1" type="number" v-model="state.coeff" />
              *
              <InputText class="p-inputtext-sm text-sm w-14" step="0.1" type="number" v-model="state.currency" />
            </span>
          </div> -->
        </div>
        <div v-for="(field, index) in state.numberFields" :key="index">
          <span class="text-gray-500 dark:text-gray-200 ml-2 text-sm font-bold">{{ trnColumns(field) }}</span>
          <Inplace :closable="true">
            <template #display>
              <span v-if="state.product[field] || state.product[field] === 0">{{ state.product[field] }}</span>
              <span class="text-sm" v-else>Редактировать. <i class="pi pi-pencil"></i></span>
            </template>
            <template #content>
              <InputNumber :maxFractionDigits="2" :step="state.nfp[field] || 1" type="number" v-model="state.product[field]" />
            </template>
          </Inplace>
        </div>
        <div class="ml-2">
          <div class="mt-3" v-for="(field, index) in state.editorFields" :key="index">
            <span class="text-gray-500 dark:text-gray-200 text-sm font-bold">{{ trnColumns(field) }}</span>
            <Editor class="border p-2 mt-2 rounded-lg" v-model="state.product[field]" editorStyle="height: 100px" />
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="showRKform mt-5"></div>
  <div v-if="state.showRK" class="p-2 text-gray-500 dark:text-zinc-100 font-semibold text-base">Ремкомлект</div>
  <div
    v-if="state.showRK"
    :class="state.showRKhighlight && 'border-4 border-yellow-300'"
    class="transition-all ease-in-out duration-300 sm:w-1/3 bg-gray-100 dark:bg-zinc-800 shadow-sm p-2 px-4 rounded-md mt-5"
  >
    <div class="flex justify-center">
      <AutoComplete
        dataKey="prod_id"
        :forceSelection="false"
        v-model="state.RKfilterModel"
        :suggestions="state.filteredProds"
        @item-select="addToRK($event)"
        @complete="findRKitemHandler($event)"
        placeholder="Добавить товар"
        _dropdown="true"
      >
        <template #item="slotProps">
          <div class="flex items-center justify-left space-x-5 z-50">
            <img class="w-16 rounded-md shadow" :alt="slotProps.item.prod_img" :src="'https://mirsalnikov.ru/data/rti/' + slotProps.item.prod_img + '.jpg'" />
            <div class="font-bold text-gray-700 dark:text-zinc-100">{{ slotProps.item.prod_analogsku }}</div>
            <div class="text-gray-700 dark:text-zinc-200">{{ slotProps.item.prod_sku }}</div>
            <div class="text-gray-600 dark:text-zinc-300">{{ slotProps.item.prod_size }}</div>
          </div>
        </template>
      </AutoComplete>
    </div>

    <Divider />

    <div v-for="(item, index) in state.product.rk" :key="index">
      <div
        :class="(item.new ? 'bg-green-50 dark:bg-green-300/20 rounded-md ' : '') + (item.min ? ' bg-red-50 dark:bg-red-300/20 rounded-md ' : '')"
        class="p-2 flex justify-between items-center space-x-5 text-gray-600 dark:text-gray-200"
      >
        <!-- <div>
                                <img class="w-20 rounded-md shadow" :alt="item.state.product.prod_img" :src="'https://mirsalnikov.ru/data/rti/' + item.state.product.prod_img + '.jpg'" />
                            </div> -->
        <div>
          <div>
            <span class="text-slate-500 dark:text-gray-300 font-bold">{{ index + 1 }}.</span> <b>{{ item.product.prod_analogsku }}</b> / {{ item.product.prod_sku }}
          </div>
          <!-- <div>{{item.state.product.prod_sku}}</div> -->
          <div>{{ item.product.prod_manuf }} / {{ item.product.prod_size }}</div>
          <div>
            Склад: <b>{{ item.product.prod_count }}</b>
          </div>
        </div>
        <div class="">
          <InputNumber
            @input="RKinputChangeHandler($event, item.id)"
            inputClass="w-10"
            v-model="item.qty"
            showButtons
            buttonLayout="horizontal"
            :step="1"
            :min="0"
            :allowEmpty="false"
            autofocus
            decrementButtonClass="p-button-secondary p-1"
            incrementButtonClass="p-button-secondary p-1"
            incrementButtonIcon="pi pi-plus"
            decrementButtonIcon="pi pi-minus"
          />
        </div>
      </div>
      <Divider />
    </div>
  </div>

  <div class="grid grid-cols-1 gap-6 md:grid-cols-2 rounded pt-7">
    <div class="bg-white dark:bg-zinc-900 rounded-md p-3">
      <span class="text-gray-500 dark:text-gray-200 mt-5 text-sm">Основные фото</span>
      <div class="mt-1 grid grid-cols-1 md:grid-cols-2 gap-5 space-x-5 rounded p-5">
        <div>
          <span class="text-gray-500 dark:text-gray-200 font-bold text-lg">mirsalnikov.ru</span>
          <div>
            <Image imageClass="max-w-64" preview :src="`https://mirsalnikov.ru/data/rti/${state.product.prod_img}.jpg?v=${Math.random()}`" :alt="state.product.prod_img">
              <template #indicator> Увеличить </template>
            </Image>
          </div>
          <Inplace :closable="true">
            <template #display>
              <span>{{ state.product.prod_img }} <i class="pi pi-pencil"></i></span>
            </template>
            <template #content>
              <InputText class="w-32" type="text" v-model="state.product.prod_img" />
            </template>
          </Inplace>
        </div>
        <div>
          <span class="text-gray-500 dark:text-gray-200 font-bold text-lg">rumisota.eu</span>
          <div>
            <Image imageClass="max-w-64" preview :src="`https://rumisota.eu/data/rumi/${state.product.prod_img_rumi}.jpg?v=${Math.random()}`" :alt="state.product.prod_img">
              <template #indicator> Увеличить </template>
            </Image>
          </div>
          <Inplace class="ml-auto" :closable="true">
            <template #display>
              <span>{{ state.product.prod_img_rumi }} <i class="pi pi-pencil"></i></span>
            </template>
            <template #content>
              <InputText class="w-32" type="text" v-model="state.product.prod_img_rumi" />
            </template>
          </Inplace>
        </div>
      </div>
      <div class="flex justify-end space-x-5">
        <FileUpload
          mode="basic"
          name="mainImg[]"
          :url="getUploadImgURL()"
          accept="image/*"
          :maxFileSize="10000000"
          @upload="uploadHandler"
          :auto="true"
          class="p-button-text"
          chooseLabel="Загрузить новое"
        />
        <!-- <Button @click="() => imageEditorVisible = true" label="editor" /> -->
        <FileUpload class="p-button-text" auto mode="basic" accept="image/*" maxFileSize="1000000" chooseLabel="Загрузить через редактор" :customUpload="true" @uploader="handleFileUpload" />
      </div>
    </div>

    <div class="bg-white dark:bg-zinc-900 rounded-md p-3">
      <span class="text-gray-500 dark:text-gray-200 mt-5 text-sm">Дополнительные фото</span>
      <div class="flex justify-between gap-6">
        <div class="w-1/2">
          <Galleria class="col-span-" :value="state.product.images" :numVisible="5" :circular="true" :showItemNavigators="true">
            <template #item="slotProps">
              <div class="flex">
                <div>
                  <Image preview :src="returnBasePath() + slotProps.item.path" :alt="slotProps.item.name" />
                </div>
                <div class="absolute right-5 top-5">
                  <Button :pt="{ root: 'bg-red-200' }" size="small" :ptOptions="{ mergeProps: true }" @click="() => deleteImage(slotProps.item)" icon="pi pi-times" severity="danger" />
                </div>
              </div>
            </template>
            <template #thumbnail="slotProps">
              <Image :src="returnBasePath() + slotProps.item.path + '?v=' + Math.random()" :alt="slotProps.item.name" style="display: block" />
            </template>
          </Galleria>
        </div>
        <div class="w-1/2">
          <FileUpload name="addImgs[]" :url="getUploadImgURL()" @upload="uploadHandler" :multiple="true" accept="image/*" :maxFileSize="100000000">
            <template #content>
              <ul v-if="uploadedFiles && uploadedFiles[0]">
                <li v-for="file of uploadedFiles[0]" :key="file">{{ file.name }} - {{ file.size }} bytes</li>
              </ul>
            </template>
            <template #empty>
              <p>Перетащите файлы для загрузки</p>
            </template>
          </FileUpload>
        </div>
      </div>
    </div>
  </div>

  <Dialog dismissableMask modal _position="top" header="Обновить группу товаров" v-model:visible="state.updateGroupModal"> обновить группу </Dialog>

  <Dialog dismissableMask modal _position="top" header="Заказы с этим товаром" v-model:visible="state.displayOrderList">
    <DataTable table-class="table-default" exportable :lazy="true" responsiveLayout="stack" breakpoint="900px" stripedRows :value="state.orderList">
      <Column sortable field="id" header="Заказ">
        <template #body="{ data }">
          <router-link class="hover:underline" :to="'/orders/' + data.id">{{ data.id }} </router-link>
        </template>
      </Column>
      <Column sortable field="qty" header="Кол-во"></Column>
      <Column field="date" header="Дата"></Column>
      <Column sortable field="client.name" header="Клиент">
        <template #body="{ data }">
          <router-link class="hover:underline" :to="'/clients/' + data.client.id">{{ data.client.name }} </router-link>
        </template>
      </Column>
    </DataTable>
  </Dialog>

  <div v-if="state.displayChangeList">
    <Dialog dismissableMask v-model:visible="state.displayChangeList" modal maximizable class="w-full" header="История изменений товара">
      <div class="mb-5 shadow-xl shadow-slate-200 dark:shadow-none p-3 rounded-md" :key="snapshot.id" v-for="(snapshot, index) in state.snapshots">
        <div class="flex justify-between">
          <div class="p-text-secondary font-semibold mb-5">{{ snapshot.user }}</div>
          <div class="p-text-secondary">{{ dayjs(snapshot.created_at).format('DD.MM.YY HH:mm') }}</div>
        </div>
        <div :key="key" v-for="key in Object.keys(snapshot.body)">
          <div class="flex items-center space-x-3" v-if="state.snapshots[index + 1]?.body && snapshot.body[key] !== state.snapshots[index + 1]?.body?.[key]">
            <di class="flex items-center space-x-2">
              <div>{{ trnColumns(key) }}:</div>
              <div v-html="typeof state.snapshots[index + 1]?.body?.[key] == 'object' ? ' ' : state.snapshots[index + 1]?.body?.[key]" class="font-semibold text-zinc-500" />

              <i v-if="typeof snapshot.body[key] != 'object'" class="mx-2 text-sm pi pi-arrow-right" />
              <div v-html="typeof snapshot.body[key] == 'object' ? ' ' : snapshot.body[key]" class="font-semibold text-red-500" />
            </di>
          </div>
        </div>
      </div>
    </Dialog>
  </div>

  <Dialog dismissableMask _position="top" header="Добавить к спросу" v-model:visible="state.displayAddStatForm">
    <div class="flex flex-col space-y-3">
      <div>
        <InputText v-model="state.addStatForm.qty" placeholder="Кол-во" label="Кол-во" class="p-inputtext-sm text-sm" step="0.1" type="number" />
      </div>
      <div>
        <InputText v-model="state.addStatForm.notice" placeholder="Примечание" class="p-inputtext-sm text-sm" />
      </div>
    </div>
    <div class="flex justify-end mt-4">
      <Button @click="addStat($event)" :loading="state.isLoadingSave" label="Добавить" icon="pi pi-plus" class="p-button-primary p-button-outlined p-button-sm" iconPos="right" />
    </div>
  </Dialog>

  <Dialog
    :pt="{
      root: '!w-11/12 !overflow-scroll'
    }"
    class="!w-11/12 !overflow-scroll"
    :pt-options="{ mergeProps: true }"
    dismissableMask
    maximizable
    modal
    _position="top"
    header="Cхема товара"
    v-model:visible="state.displaySchemaForm"
  >
    <ProductMapArea @delete="deleteSchemaHandler" @success="createSchemaHandler" :init-data="state.product?.schema?.body" :product="{ ...state.product }" />
  </Dialog>

  <OverlayPanel style="max-height: 400px; overflow-y: scroll" ref="stat">
    <div>
      <div class="mb-5 text-lg font-bold text-gray-600 dark:text-gray-200">Заметки по статистике спроса:</div>
      <div class="space-y-3 flex flex-col" v-for="(item, key, index) in state.product.statlist" :key="index">
        <div class="flex flex-wrap justify-between items-center space-x-3">
          <div>
            {{ item.client_ip }} <span v-if="item.client_info"> | {{ item.client_info }}</span> |
            <b>{{ item.count }}</b>
          </div>
          <div>
            {{ dayjs(item.created_at).format('DD.MM.YYYY HH:mm') }}
          </div>
          <Button @click="(e) => removeStatItem(e, item.id)" text severity="danger" label="Удалить" />
        </div>
        <hr />
      </div>
    </div>
  </OverlayPanel>

  <OverlayPanel showCloseIcon class="w-4/5" @show="getDuplicats" ref="duplicatsPanel">
    <div :class="'find-product-component' + state.product?.prod_id"></div>

    <PickList v-model="state.duplicats" listStyle="height:90%" dataKey="prod_id">
      <template #sourceheader>
        <div class="flex items-center justify-between space-x-2">
          <span>Дубликаты</span>
          <ProgressSpinner v-if="state.duplicatsLoading" style="width: 50px; height: 50px" strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" />
        </div>
      </template>
      <template #targetheader> На объединение </template>
      <template #item="{ item, index }">
        <div class="state.product-item">
          <div class="text-left">
            <div class="flex justify-between space-x-3 items-center">
              <div>
                <span class="bg-gray-200 dark:bg-zinc-700 rounded-full px-2 py-1 font-bold mr-2">{{ index + 1 }}</span>
                <b>{{ item.prod_sku }}</b> / {{ item.prod_analogsku }} / id: {{ item.prod_id }}
              </div>
              <div>
                <a target="_blank" :href="'/products/' + item.prod_id">
                  <Button icon="pi pi-external-link" text rounded class="border-none p-button-sm" />
                </a>
                <Button @click="() => showMergeProductHandler(item)" icon="pi pi-eye" text rounded class="border-none p-button-sm" />
              </div>
            </div>

            <div>
              Наличие: <b>{{ item.prod_count }}</b>
            </div>

            <div class="mt-3">
              Материал:
              <b class="py-1 px-2 rounded-sm bg-yellow-100 dark:bg-yellow-600/70 shadow-sm" :style="item.prod_material != state.product.prod_material ? 'color:red' : ''">{{ item.prod_material }}</b>
            </div>
            <div class="mt-2">
              Тип: <b class="py-1 px-2 rounded-sm bg-orange-100 dark:bg-orange-600/70 shadow-sm">{{ item.prod_type }}</b>
            </div>
            <div class="mt-2">
              Размер: <b>{{ item.prod_size }}</b>
            </div>

            <div class="mt-3">Бренд: {{ item.prod_manuf }}</div>
            <div v-if="item.prod_model">Модель: {{ item.prod_model }}</div>
            <!-- <div>{{item.prod_note}}</div> -->
            <div>Назнач: {{ item.prod_purpose }}</div>
            <div v-if="item.prod_secret">Секрет: {{ item.prod_secret }}</div>
            <div>Примен: {{ item.prod_uses }}</div>
            <div class="mt-3" v-if="item.prod_group">Группа: {{ item.prod_group }}</div>
          </div>
        </div>
      </template>
    </PickList>
    <div class="flex justify-between mt-5 mb-3">
      <FindProduct deepSwitch class="w-full" @select="addToDuplicats"></FindProduct>
      <Button text @click="initMerge" class="p-button-secondary" label="Объединить" />
    </div>
  </OverlayPanel>

  <ConfirmDialog></ConfirmDialog>

  <Dialog @hide="() => (state.imageEditorVisible = false)" v-model:visible="state.imageEditorVisible" modal header="Редактировать изображение" :style="{ width: '90%', height: '100vh' }">
    <div v-if="state.imageEditorVisible">
      <ImageEditor @save="saveEditorHandler" :img-file="state.newImgFile" />
    </div>
  </Dialog>

  <div v-show="printStickerVisible">
    <div class="sticker-container">
      <div class="text-zinc-900 font-semibold text-center">
        <div class="space-x-3">
          <span>
            {{ state.product?.prod_purpose }}
          </span>
          <span>
            {{ state.product?.prod_uses }}
          </span>
        </div>
        <div>{{ String(state.product?.prod_type).replace(/[А-я]*/gimu, '') }}</div>
        <div>
          {{ state.product?.prod_sku }}
        </div>
        <div>
          {{ state.product?.prod_analogsku }}
        </div>
      </div>
    </div>
  </div>

  <div v-if="showAnyProduct">
    <Dialog
      @hide="() => (showAnyProduct = false)"
      :append-to="'.find-product-component' + state.product?.prod_id"
      modal
      maximizable
      class="w-4/5"
      :header="modalProduct.prod_purpose + ' ' + modalProduct.prod_analogsku"
      v-model:visible="showAnyProduct"
    >
      <ProductCard @update="(e: any) => (showAnyProduct = false)" :productData="modalProduct"></ProductCard>
    </Dialog>
  </div>
</template>
