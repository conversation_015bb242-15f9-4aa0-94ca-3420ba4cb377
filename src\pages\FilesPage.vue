<script setup lang="ts">
import UploadFiles from '@/components/UploadFiles.vue'
import { api } from '@/lib/_api'
import { useQuery } from '@tanstack/vue-query'
import Button from 'primevue/button'
import ConfirmDialog from 'primevue/confirmdialog'
import Image from 'primevue/image'
import InputText from 'primevue/inputtext'
import Listbox from 'primevue/listbox'
import InputSwitch from 'primevue/inputswitch'

import Paginator from 'primevue/paginator'
import ProgressSpinner from 'primevue/progressspinner'
import RadioButton from 'primevue/radiobutton'
import ScrollPanel from 'primevue/scrollpanel'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import { onMounted, watch } from 'vue'
import { ref } from 'vue'
import TabMenu from 'primevue/tabmenu'

type Target = 'rti' | 'rumi' | 'docs'

interface ServerFiles {
  files: FileListItem[]
  total: number
  page: number
  pageSize: number
  search: string | undefined
}

interface FileListItem {
  fullname: string
  path: string
  ext: string
  name: string
  target: string
}

const target = ref<Target>('rti')
const files = ref<ServerFiles>()
const selectedFiles = ref<FileListItem[]>([])
const activeTab = ref()

const searchvalue = ref('')
const pageNumber = ref(1)
const pageSize = ref(100)

const toast = useToast()
const confirm = useConfirm()

const applyToProduct = ref(true)

const items = ref([
  { label: 'изобр. RTI', dir: 'rti', icon: 'pi pi-image' },
  { label: 'изобр. RUMISOTA', dir: 'rumi', icon: 'pi pi-image' },
  { label: 'Документы', dir: 'docs', icon: 'pi pi-file-excel' }
])

const { isLoading, isFetching, isSuccess, isError, data, error, refetch } = useQuery({
  queryKey: [target, searchvalue, pageSize, pageNumber],
  queryFn: getFiles,
  //   onError: (e) => apiErrorToast(e),
  refetchOnWindowFocus: false,
  onSuccess: (data) => {
    files.value = data
  }
})

watch(files, () => {
  //console.log('@@@files:', files.value?.files.length)
})

watch(activeTab, () => {
  target.value = items.value[activeTab.value].dir as Target
})

watch(searchvalue, () => {
  if (searchvalue.value == '') {
    pageNumber.value = 1
    pageSize.value = 100
  }
})

watch(target, () => {
  searchvalue.value = ''
  pageNumber.value = 1
  pageSize.value = 100
})

function changePageHandler({ page, rows }: any) {
  // //console.log({ page, rows })
  pageSize.value = rows
  pageNumber.value = page + 1
}

function downloadFile(file: FileListItem) {
  window.open('https://mirsalnikov.ru/' + file.path)
}

async function getFiles() {
  const qs = new URLSearchParams()
  qs.set('target', target.value)
  qs.set('search', searchvalue.value)
  qs.set('page', String(pageNumber.value || 1))
  qs.set('pageSize', String(pageSize.value || 100))

  const res = await api('/cpan/files/list?' + qs.toString())

  if (!res.ok) {
    throw new Error('Failed to fetch products')
  }

  return res.body
}

async function deleteItem(event, item: FileListItem) {
  confirm.require({
    message: `Удалить файл "${item.name}"?`,
    target: event.currentTarget,
    header: 'Подтвердить действие',
    icon: 'pi pi-exclamation-triangle',
    acceptIcon: 'pi pi-check',
    acceptLabel: 'Продолжить',
    rejectLabel: 'Отмена',
    accept: async () => {
      const res = await api('/cpan/files/remove?target=' + target.value + '&fileName=' + item.fullname)

      if (!res.ok) {
        throw new Error('Failed to delete file')
      }

      toast.add({
        severity: 'success',
        'summary': 'Файл успешно удален'
      })

      refetch()
    },
    reject: () => {},
    onHide: () => {}
  })
}

onMounted(() => {
  try {
    document.title = 'Файлы'
  } catch (error) {}
})
</script>

<template>
  <div class="containter md:flex md:px-10 justify-center items-start space-x-5 md:space-x-10 md:mt-5">
    <div class="md:w-2/6">
      <!-- <span class="font-semibold">Разделы</span>
      <div class="bcard p-3 mt-2">
        <div v-for="(item, key) in ['rti', 'rumi', 'docs']" :key="key" class="flex items-center space-y-3 mt-3 b">
          <RadioButton v-model="target" :inputId="item" name="pizza" :value="item" />
          <label :for="key" class="ml-2 uppercase">{{ item }}</label>
        </div>
      </div> -->
      <div class="text-lg font-semibold">Загрузка файлов</div>
      <div class="bcard my-2">
        <div>
          <div class="flex items-center space-x-3 bg-yellow-50 dark:bg-rose-900/50 shadow-sm p-2 rounded text-left">
            <span class="font-semibold">Прикрепить фото к товарам</span>
            <InputSwitch v-model="applyToProduct" />
          </div>
          <div class="bg-yellow-50 dark:bg-rose-900 shadow-sm p-2 rounded text-left">Фото с названием файлов которые
            совпадают с артикулом товара, будут привязаны к этому товару.</div>
        </div>
        <div class="mt-3">
          <UploadFiles @upload="getFiles" :applyToProduct="applyToProduct" />
        </div>
        <div class="text-center">файлы <span class="font-bold">xlsx/docs</span> или <span
            class="font-bold">изображения</span> будут автоматически определены в свой раздел</div>
      </div>
    </div>
    <div class="md:w-full">
      <div class="font-semibold text-lg">Файлы на сервере</div>
      <div class="bcard p-5 mt-3">
        <div class="flex justify-center w-full">
          <TabMenu v-model:activeIndex="activeTab" :model="items" />
        </div>
        <div class="flex justify-between items-center mt-5">
          <div class="font-semibold">Показаны {{ files?.pageSize > files?.total ? files?.total : files?.pageSize }} из
            {{ files?.total }}</div>
          <div class="flex items-stretch space-x-2 mr-3">
            <span>
              <InputText :pt="{
                  root: {
                    class: 'font-semibold text-lg'
                  }
                }" 
                :pt-options="{mergeProps: true}"
                class="font-semibold" v-model="searchvalue" placeholder="Поиск" />
              <i class="-ml-5 pi pi-search" />
            </span>
            <!-- <InputText class="w-full" v-model="searchvalue" /> -->
            <!-- <Button icon="pi pi-search" /> -->
          </div>
        </div>
        <ScrollPanel style="width: 100%; height: 600px" class="custom mt-5">
          <div v-if="!isLoading" class="space-y-3">
            <div v-for="(file, key) in files?.files || []" :key="key"
              class="flex justify-between items-center hover:dark:bg-zinc-700 p-1 rounded-lg">
              <div class="flex items-center space-x-5">
                <Image preview v-if="target != 'docs'" :alt="file.name" :src="'https://mirsalnikov.ru/' + file.path"
                  class="w-20 rounded-lg" />
                <div class="font-semibold">
                  <span class="font-semibold text-ellipsis">{{ String(file.fullname).slice(0, 20) }}<span
                      v-show="String(file.fullname).length > 20">...</span></span>
                </div>
              </div>
              <div>
                <Button @click="() => downloadFile(file)" severity="secondary" text label="Скачать" />
                <Button @click="(e) => deleteItem(e, file)" severity="danger" text label="Удалить" />
              </div>
            </div>
          </div>
          <div class="flex items-center justify-center p-5" v-else>
            <ProgressSpinner />
          </div>
        </ScrollPanel>
        <Paginator @page="changePageHandler" :alwaysShow="false" :rows="files?.pageSize" :totalRecords="files?.total"
          :rowsPerPageOptions="[100, 200, 300]"></Paginator>
      </div>
    </div>
  </div>
  <ConfirmDialog></ConfirmDialog>
</template>
