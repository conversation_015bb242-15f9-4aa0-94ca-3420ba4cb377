<script setup lang="ts">
import { computed, onBeforeMount, onMounted, onUpdated, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'

import { trnColumns } from '@/lib/trnColumns'
import { api } from '@/lib/_api'

import Paginator from 'primevue/paginator'
import InputNumber from 'primevue/inputnumber'
import Dropdown from 'primevue/dropdown'

import DataView from 'primevue/dataview'
import ProgressBar from 'primevue/progressbar'
import TieredMenu from 'primevue/tieredmenu'
import Chip from 'primevue/chip'
import ClientData from '@/components/client/ClientData.vue'
import { useQuery, useQueryClient } from '@tanstack/vue-query'

import ScrollPanel from 'primevue/scrollpanel'
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'
import InputText from 'primevue/inputtext'
import ProgressSpinner from 'primevue/progressspinner'

let delayTimer

const route = useRoute()
const router = useRouter()

const confirm = useConfirm()
const toast = useToast()

const loading = ref(false),
  totalRecords = ref(0),
  currentPage = ref(1),
  pageSize = ref(10),
  clients = ref(),
  createNewModal = ref(false),
  globalSearchValue = ref(''),
  selectedProduct = ref(),
  copySelectedProduct = ref(),
  showProductCard = ref(),
  filters = ref({}),
  lazyParams = ref({}),
  showSl = ref(true),
  columns = ref([]),
  editingRows = ref([]),
  lockedRows = ref([]),
  cm = ref(),
  showColumns = ref(['client_id', 'client_name', 'client_mail', 'client_phone']),
  sortField = ref(''),
  sortOrder = ref(''),
  sortByValue = ref(),
  createModalShow = ref(false),
  newClient = ref({}),
  newOrg = ref({})

const menu = ref()
const items = ref([
  {
    label: 'В разработке: '
  },
  {
    separator: true
  },
  {
    label: 'Счет (коммерческое)',
    icon: 'pi pi-fw pi-external-link'
  },
  {
    label: 'Акт сверки',
    icon: 'pi pi-fw pi-external-link'
  }
])

const queryClient = useQueryClient()
let URL = new URLSearchParams(window.location.search)

if (!URL.has('limit')) {
  URL.set('limit', '20')
}

const toggle = (event) => {
  menu.value.toggle(event)
}

const { isLoading, isFetching, isSuccess, isError, data, error, refetch } = useQuery({
  queryKey: ['clients', route.fullPath],
  queryFn: getClients,
  onError: (e) => apiErrorToast(e),
  refetchOnWindowFocus: false,
  onSuccess: (data) => {
    loading.value = true

    columns.value = data?.columns
      ?.map((c) => ({
        field: c,
        header: trnColumns(c)
      }))
      .filter((i) => i.header != '--')

    clients.value = data.clients?.data

    if (data.clientsByOrgs?.length) {
      clients.value.push(...data.clientsByOrgs)
    }

    totalRecords.value = data.clients?.meta.total

    pageSize.value = data.clients?.meta.per_page
    currentPage.value = data.clients?.meta.current_page
    loading.value = false
  }
})

async function getClients() {


  const res = await api('/cpan/clients?' + URL.toString())
  if (res.ok) {
    return res.body
  } else {
    throw new Error(`${res.status}: ${res.statusText}: ${res.message}`)
  }
}

async function loadLazyData() {
  // if (!URL.has('page')) URL.set('page', 1)
  // if (!URL.has('limit')) URL.set('limit', 20)
  // if (!URL.has('sortField')) URL.set('sortField', 'client_id')

  queryClient.invalidateQueries()
}

function deleteProduct(product) {
  confirm.require({
    message: `Удалить ${product.prod_purpose} ${product.prod_analogsku} ?`,
    header: 'Подтвердить действие',
    icon: 'pi pi-exclamation-triangle',
    acceptLabel: 'Удалить',
    rejectLabel: 'Отмена',
    accept: async () => {
      try {
        const res = await api('/cpan/clients/delete/' + product.prod_id)
        loadLazyData()

        res.ok ? toast.add({ severity: 'success', summary: 'Товар удален', detail: '', life: 3000 }) : apiErrorToast(res)
      } catch (error) {
        apiErrorToast({ status: 'browser', statusText: error })
      }
    },
    reject: () => {}
  })
  selectedProduct.value = null
}

function apiErrorToast(res) {
  toast.add({ severity: 'error', summary: `Ошибка cервера`, detail: `${res.status} ${res.statusText} ${res.message || ''}`, life: 6000 })
}

function routerPush() {
  loadLazyData()
  router.push({ query: Object.fromEntries(URL) })
}

function onPage(event) {
  URL.set('page', event.page + 1)
  URL.set('limit', event.rows)

  routerPush()
}

function onSort(event) {
  //console.log('onSort', event)

  // sortOrder.value = 1;
  sortField.value = event.value?.field || 'client_id'

  if (URL.get('sortField') == sortField.value) {
    sortOrder.value == 1 ? (sortOrder.value = -1) : (sortOrder.value = 1)
  }

  URL.set('sortField', sortField.value)
  URL.set('sortOrder', sortOrder.value == -1 ? 'asc' : 'desc')

  routerPush()
}

function onFilter(event) {
  let _filters = {}
  Object.keys(filters.value).map((key) => (filters.value[key].value ? (_filters[key] = filters.value[key]) : ''))

  URL.set('filters', JSON.stringify(_filters))

  routerPush()
}

const activeColumns = computed(() => showColumns.value.map((i) => ({ field: i, header: trnColumns(i) })))

watch(currentPage, () => {
  URL.set('page', currentPage.value || 1)
  routerPush()
})

watch(showColumns, () => {
  if (showColumns.value) {
    let val = String(showColumns.value.join(','))
    URL.set('showcolumns', val)
    // window.history.replaceState({}, route.fullPath, route.path + '/?' + URL.toString())
    routerPush()
  }
})

watch(showColumns, () => showColumns.value.map((col) => (filters.value[col] = { value: '', matchMode: 'contains' })))

// watch(globalSearchValue, () => {
//     clearTimeout(delayTimer)
//     delayTimer = setTimeout(function () {
//         URL.set('searchvalue', globalSearchValue.value)
//         // router.push({ query: getQueryParams('fromWatchSearchvalue') })
//         // loadLazyData()
//     }, globalSearchValue.value != '' ? 1000 : 100)
// })

async function startSearch(e) {
  URL.set('searchvalue', globalSearchValue.value) //
  routerPush()
}

const createNewClient = async () => {
  let payload = {}

  Object.keys(newClient.value).map((key) => {
    if (key.startsWith('client_')) {
      payload[key] = newClient.value[key]
    }
  })

  if (newClient.value.org) {
    payload.org = newClient.value.org

    delete payload.org.created_at
    delete payload.org.updated_at
  }

  //console.log('payload: ', payload)
  // return payload

  try {
    const res = await api('/cpan/client/update', {
      method: 'POST',
      data: payload
    })

    if (res.ok) {
      toast.add({ severity: 'success', summary: 'Данные клиента обновлены', detail: '', life: 3000 })
    } else {
      toast.add({ severity: 'error', summary: 'Ошибка', detail: res.message, life: 6000 })
    }
  } catch (error) {
    toast.add({ severity: 'error', summary: 'Ошибка cервера', detail: error, life: 6000 })
  }
}

onBeforeMount(() => {
  showColumns.value.map((col) => (filters.value[col] = { value: '', matchMode: 'contains' }))
})

onMounted(async () => {
  URL = new URLSearchParams(window.location?.search)

  let { filters: _filters, sortField: _sortField, sortOrder: _sortOrder, searchvalue, showcolumns } = Object.fromEntries(URL.entries())

  if (showcolumns) {
    try {
      showColumns.value = showcolumns.split(',')
    } catch (error) {
      console.error(error)
    }
  }

  if (_filters) {
    try {
      _filters = JSON.parse(_filters)
      Object.keys(_filters).map((key) => (filters.value[key] = _filters[key]))
    } catch (error) {
      console.warn(error)
    }
  }

  if (_sortField) {
    sortField.value = _sortField
    sortOrder.value = _sortOrder == 'asc' ? -1 : 1

    sortByValue.value = {
      field: _sortField,
      header: trnColumns(_sortField)
    }
  }

  if (searchvalue) {
    globalSearchValue.value = decodeURIComponent(searchvalue)
  }

  await loadLazyData()

  try {
    document.title = 'Клиенты'
  } catch (error) {}
})
</script>

<template>
  <div class="card">
    <div v-if="isFetching" class="absolute flex justify-center w-full py-5">
      <ProgressSpinner />
    </div>
    <DataView
      :lazy="true"
      :loading="isFetching"
      :value="clients"
      :paginator="true"
      paginatorPosition="both"
      :rowsPerPageOptions="[10, 20, 50, 100, 200, 500, 1000]"
      :rows="pageSize"
      :first="Math.ceil((currentPage - 1) * pageSize)"
      :totalRecords="totalRecords"
      :sortField="sortField"
      :sortOrder="sortOrder"
      layout="list"
      @page="onPage($event)"
    >
      <template #header>
        <div class="grid grid-cols-2">
          <div>
            <Dropdown v-model="sortByValue" :options="columns" placeholder="Сортировать по" _optionValue="field" optionLabel="header" @change="onSort($event)" :showClear="true">
              <template #option="slotProps">
                <div class="p-dropdown-car-option">
                  <span>
                    {{ slotProps.option.header }}
                    <!-- <i class="pi pi-sort-amount-down"/> -->
                  </span>
                </div>
              </template>
            </Dropdown>
          </div>
          <!-- <div>
                            <Dropdown v-model="sortKey" :options="sortOptions" optionLabel="label" placeholder="Sort By Price" @change="onSortChange($event)"/>
                        </div>
                        <div>
                            <DataViewLayoutOptions v-model="layout" />
                        </div> -->
          <div class="flex justify-end space-x-5">
            <Button severity="secondary" label="Новый" text icon-pos="right" icon="pi pi-plus" @click="() => (createModalShow = true)" />
            <div>
              <InputText class="h-12 rounded-r-none" v-model="globalSearchValue" placeholder="Поиск" />
              <Button :loading="isFetching" text @click="startSearch" icon="pi pi-search" iconPos="right" class="h-12 rounded-l-none" label="Найти" />
            </div>
          </div>
        </div>
      </template>

      <template #list="slotProps">
        <div v-for="(item, index) in slotProps.items" :key="index" class="my-3 p-3 shadow-sm">
          <div class="__info grid sm:grid-cols-4 place-items-left content-center items-center space-x-3 text-gray-800 dark:text-zinc-100">
            <router-link :to="'/clients/' + item.client_id">
              <span class="text-gray-400 dark:text-zinc-100">{{ index + 1 }}.</span> {{ item.client_name }}
              <div class="ml-4 text-slate-700 dark:text-zinc-400" v-if="item.org">
                {{ item.org.org_name }} <span class="text-gray-500 dark:text-gray-200 text-sm">(#{{ item.org.org_id }})</span>
              </div>
              <div class="pl-4 text-gray-500 dark:text-gray-200 font-semibold mt-2">
                Номер: {{ item.client_number }}
                <p>
                  <span class="text-xs text-gray-600 dark:text-gray-200">(#: {{ item.client_id }})</span>
                </p>
                <p class="mt-2 text-sm">
                  <!-- {{item.org?.org_name || ''}} -->
                  Рейтинг: <Chip class="text-sm">Нейтральный</Chip>
                </p>
              </div>
            </router-link>
            <div class="text-center">
              {{ item.client_phone }}
              <p class="text-gray-500 dark:text-gray-200 font-semibold">{{ item.client_mail }}</p>
              <div>
                <div class="bg-blue-50 dark:bg-zinc-700 p-2 text-slate-600 dark:text-zinc-100 font-semibold text-sm rounded-md w-30 mx-auto">
                  {{ item.client_country?.match(/[A-Za-z]*/gm).filter((i) => i).length ? 'RUMISOTA' : 'РТИ' }}
                </div>
              </div>
            </div>
            <div>
              {{ item.client_postindex }}, {{ item.client_country }}, {{ item.client_city }},
              {{ item.client_street }}
              {{ (item.client_house && 'д.' + item.client_house) || '' }}
              {{ (item.client_flat && 'кв.' + item.client_flat) || '' }}
            </div>

            <div class="shadow-xl shadow-slate-200 dark:shadow-none p-2 m-1">
              <!-- <div>Всего заказов: <span class="font-bold">0</span></div> -->
              <!-- <div class="text-green-700">Сумма заказов: <span class="font-bold">0 руб.</span></div> -->
              <!-- <div class="text-red-400">Сумма задолженности: <span class="font-bold">0 руб.</span></div> -->
              <div class="mt-2">
                <Button
                  text
                  class="p-button-sm p-button-secondary p-button-text"
                  icon="pi pi-book"
                  type="button"
                  label="Документы"
                  @click="toggle"
                  aria-haspopup="true"
                  aria-controls="overlay_tmenu"
                />
                <TieredMenu id="overlay_tmenu" ref="menu" :model="items" :popup="true" />
              </div>
            </div>
          </div>
        </div>
      </template>
    </DataView>
    <!-- </ScrollPanel> -->
    <Dialog dismissableMask modal header="Новый клиент" v-model:visible="createModalShow" :breakpoints="{ '960px': '75vw', '640px': '90vw' }" :style="{ width: '70vw' }" :modal="true">
      <div class="grid grod-cols-1 sm:grid-cols-2">
        <ClientData :client="newClient"></ClientData>
      </div>
      <template #footer>
        <Button @click="createNewClient" label="Добавить" icon="pi pi-check" autofocus />
      </template>
    </Dialog>
  </div>
</template>

<style>
.p-dataview {
  /* @apply max-h-screen overflow-x-scroll; */
}
</style>
