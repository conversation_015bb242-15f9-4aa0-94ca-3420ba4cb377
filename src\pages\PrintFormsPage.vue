<script lang="ts" setup>
import { api } from '@/lib/_api'
import type { PrintForm } from '@/lib/interfaces/PrintForm'
import { useQuery } from '@tanstack/vue-query'
import Listbox from 'primevue/listbox'
import { onMounted, ref, watch } from 'vue'
import Splitter from 'primevue/splitter'
import SplitterPanel from 'primevue/splitterpanel'
import Editor from 'primevue/editor'
import Textarea from 'primevue/textarea'
import InputText from 'primevue/inputtext'
import Button from 'primevue/button'
import ScrollPanel from 'primevue/scrollpanel'
import ProgressBar from 'primevue/progressbar'
import ProgressSpinner from 'primevue/progressspinner'
import { useToast } from 'primevue/usetoast'
import Dialog from 'primevue/dialog'
import HtmlEditor from '@/components/HtmlEditor.vue'

const toast = useToast()

// Then register the languages you need
const forms = ref<PrintForm[]>([])
const selectedForm = ref<PrintForm>()
const newForm = ref<PrintForm>({})
const isNewForm = ref(false)
const editorValue = ref()
const orderID = ref('28982')
const preview = ref()
const previewEditorHTML = ref(false)
const newFormModalShow = ref(false)

const { isLoading, isFetching, isSuccess, isError, data, error, refetch } = useQuery({
  queryKey: ['q'],
  queryFn: getData,
  //   onError: (e) => apiErrorToast(e),
  refetchOnWindowFocus: false,
  onSuccess: (data) => {
    forms.value = data
  }
})

const {
  isLoading: preview_isLoading,
  isFetching: preview_isFetching,
  data: preview_data,
  error: preview_error,
  refetch: preview_refetch
} = useQuery({
  queryKey: [selectedForm.value],
  // queryKey: ['qq'],
  queryFn: getFormPreview,
  refetchOnWindowFocus: false,
  retry: 0,
  onSuccess: (data) => {
    preview.value = data
  },
  enabled: !!selectedForm.value?.page_key
})

function printElement(elementId: string) {
  const elementToPrint = document.getElementById(elementId)
  const printContainer = document.getElementById('print-container')

  if (elementToPrint && printContainer) {
    printContainer.innerHTML = elementToPrint.innerHTML
    window.print()
    printContainer.innerHTML = ''
  } else {
    throw new Error('print error: !elementToPrint || !printContainer')
  }
}

async function saveChanges() {
  // editorValue.value = undefined
  toast.add({
    severity: 'info',
    summary: 'Сохранение формы...'
  })

  const res = await api('/cpan/forms/update', {
    method: 'POST',
    data: {
      formData: {
        page_key: selectedForm.value?.page_key,
        page_body: editorValue.value?.html || editorValue.value,
        page_title: selectedForm.value?.page_title,
        page_locale: selectedForm.value?.page_locale
      }
    }
  })
  toast.removeAllGroups()

  if (res.ok) {
    toast.add({
      severity: 'success',
      summary: 'Форма обновлена',
      life: 4000
    })
    await refetch()
    preview_refetch()
    selectedForm.value = res.body
  } else {
    toast.add({
      severity: 'error',
      summary: 'Ошибка обновления формы: ' + `${res.message} ${res.statusText}`
    })
  }
}

async function loadPreviewHandler() {
  previewEditorHTML.value = true
  await preview_refetch()
  previewEditorHTML.value = false
}

async function getFormPreview() {
  //console.log({ editorValue: editorValue.value, previewEditorHTML: previewEditorHTML.value })

  const res = await api(`/cpan/forms/make?key=${selectedForm.value?.page_key}&orderId=${orderID.value}&locale=${selectedForm.value?.page_locale}`, {
    method: 'POST',
    data: {
      formHTML: previewEditorHTML.value && (editorValue.value?.html || editorValue.value)
    }
  })

  if (res.ok) {
    preview.value = res.body
    // editorValue.value = res.body
    return res.body
  } else {
    toast.add({
      severity: 'error',
      summary: 'Ошибка: ' + `${res.message} ${res.statusText}`
    })
    return new Error(res.statusText)
  }
}

async function getData() {
  const res = await api('/cpan/forms/list')

  return res.body
}

function onChangeHtmlEditorValue(value: string) {
  editorValue.value = value
}

function newFormHandler() {
  newFormModalShow.value = false
  isNewForm.value = true

  let newFormData: PrintForm = {
    ...newForm.value,
    page_body: '<div> </div>'
  }

  previewEditorHTML.value = true
  editorValue.value = newFormData.page_body
  forms.value.unshift(newFormData)

  selectedForm.value = newFormData
}

watch(selectedForm, (value, oldValue) => {
  //   editorValue.value = value?.page_body
  selectedForm.value?.page_key ? preview_refetch() : false
})

// watch(orderID, (v, ov) => {
//   if (v !== ov) {
//     preview_refetch()
//   }
// })

onMounted(async () => {
  try {
    document.title = 'Печатные Формы'
  } catch (error) {}
})
</script>

<template>
  <div class="grid gap-10 lg:grid-cols-7">
    <div class="col-span-2">
      <div class="flex justify-between items-center">
        <div class="font-bold text-lg text-slate-500 dark:text-gray-300">Список форм</div>
        <Button @click="() => (newFormModalShow = true)" icon="pi pi-plus" text label="Новая" />
      </div>
      <div class="mt-8">
        <Listbox v-model="selectedForm" :options="forms" filter optionLabel="page_title"
          class="border-0 shadow-xl shadow-slate-200 dark:shadow-none" />
      </div>
    </div>
    <div class="col-span-5">
      <div class="flex flex-wrap justify-between">
        <div class="font-bold text-lg text-slate-500 dark:text-gray-300">Демо заказ</div>
        <div>
          <InputText @change="preview_refetch()" placeholder="номер заказа" v-model="orderID" />
          <!-- <Button @click="getFormPreview" icon="pi pi-check" text rounded /> -->
        </div>
      </div>
      <div class="mt-3 shadow-xl border shadow-slate-200 dark:shadow-none p-3 rounded-md">
        <div></div>
        <ScrollPanel class="custombar1" style="width: 100%; height: 600px">
          <!-- <ProgressBar style="height: 6px" v-if="preview_isFetching" mode="indeterminate" /> -->
          <div class="flex justify-center" v-if="preview_isFetching">
            <ProgressSpinner />
          </div>
          <div class="text-black bg-white dark:bg-zinc-300" id="formpreview" v-if="preview && !preview_isFetching" v-html="preview?.html"></div>
          <div v-if="preview_error">
            {{ preview_error }}
          </div>
        </ScrollPanel>

        <div class="flex items-center justify-end dark:bg-zinc-800 rounded-md">
          <Button @click="saveChanges" icon="pi pi-check" size="small" text label="Сохранить форму" />
          <Button @click="() => loadPreviewHandler()" icon="pi pi-eye" size="small" text
            label="Предпросмотр изменений" />
          <Button @click="() => printElement('formpreview')" icon="pi pi-print" size="small" text label="Печать" />
          <!-- <Button icon="pi pi-eye" size="small" text label="Во весь экран" /> -->
        </div>
        <ScrollPanel class="custombar1" style="width: 100%; height: 600px">
          <HtmlEditor @change="onChangeHtmlEditorValue" :value="selectedForm?.page_body || ''" />
        </ScrollPanel>
      </div>
    </div>
  </div>

  <div v-if="newFormModalShow">
    <Dialog dismissableMask v-model:visible="newFormModalShow" modal header="Новая форма" :style="{ width: '80vw' }">
      <div class="flex space-x-3 p-3">
        <div class="space-x-2">
          <span>Наименование</span>
          <InputText v-model="newForm.page_title" />
        </div>
        <div class="space-x-2">
          <span>Ключ</span>
          <InputText v-model="newForm.page_key" />
        </div>
        <div class="space-x-2">
          <span>Язык</span>
          <InputText v-model="newForm.page_locale" />
        </div>
        <!-- <div>
          <span>Содержимое</span>
          <InputText v-model="newForm.page_title" />
        </div> -->
      </div>
      <div class="flex justify-end">
        <Button @click="newFormHandler" text label="Перейти к редактированию" />
      </div>
    </Dialog>
  </div>
</template>
