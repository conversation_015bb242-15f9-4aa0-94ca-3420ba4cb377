<script setup lang="ts">
import { api } from '@/lib/_api'
import type { Client } from '@/lib/interfaces/Order'
import Message from 'primevue/message';
import { onMounted, ref } from 'vue'

const props = defineProps<{
  client: Client
}>()

const debt = ref()
async function checkDebt() {
  const res = await api('/cpan/clients/debtors?client_mail=' + props.client?.client_mail)
  if (res.ok) {
    debt.value = res.body
  }
}
onMounted(() => checkDebt())
</script>

<template>
  <Message v-if="debt?.sum" severity="warn"
    >Имеется задолженность: <span class="font-semibold">{{ debt.sum?.toLocaleString('ru-RU', { style: 'currency', currency: 'RUB' }) }}</span></Message
  >
</template>
