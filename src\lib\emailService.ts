import { trpc } from '@/tRPC'

export interface EmailSenderData {
  lastSendDate: string
  recipients: Recipient[]
  scheduleTime: number // Интервал в минутах между отправками
  enabled: boolean
  message: string
  subject: string
}

export interface Recipient {
  email: string
  note: string
  createdAt: string
  isActive: boolean
}

export interface AddEmailParams {
  email: string
  note: string
}

export interface UpdateEmailParams {
  id: string 
  payload: Partial<Recipient>
}

export const emailService = {
  // Получение данных рассылки
  async getSenderData() {
    try {
      const data = await trpc.services.getEmailSenderData.query()
      return {
        ...data,
        scheduleTime: data.scheduleTime ? new Date(data.scheduleTime) : null
      }
    } catch (error) {
      console.error('Failed to fetch sender data:', error)
      throw error
    }
  },

  // Получение списка получателей с возможностью поиска
  async getEmails(search?: string) {
    try {
      return await trpc.services.getEmailRecipients.query({ search })
    } catch (error) {
      console.error('Failed to fetch email list:', error)
      throw error
    }
  },

  // Поиск клиентов для добавления в рассылку
  async searchClients(search: string) {
    try {
      return await trpc.services.searchClients.query({ search })
    } catch (error) {
      console.error('Failed to search clients:', error) 
      throw error
    }
  },

  // Добавление нового получателя
  async addEmail(params: AddEmailParams) {
    try {
      return await trpc.services.addEmailRecipient.mutate(params)
    } catch (error) {
      console.error('Failed to add email:', error)
      throw error
    }
  },

  // Обновление получателя
  async updateEmail(id: string, payload: Partial<Recipient>) {
    try {
      return await trpc.services.updateEmailRecipient.mutate({ id, ...payload })
    } catch (error) {
      console.error('Failed to update email:', error)
      throw error 
    }
  },

  // Удаление получателя
  async deleteEmail(id: string) {
    try {
      return await trpc.services.deleteEmailRecipient.mutate({ id })
    } catch (error) {
      console.error('Failed to delete email:', error)
      throw error
    }
  },

  // Обновление данных рассылки (конфигурация и список получателей) 
  async updateSenderData(payload: Omit<EmailSenderData, 'lastSendDate'>) {
    try {
      return await trpc.services.updateEmailSenderData.mutate({
        ...payload,
        scheduleTime: payload.scheduleTime,
        lastSendDate: new Date().toISOString()
      })
    } catch (error) {
      console.error('Failed to update sender data:', error)
      throw error
    }
  },

  // Отправка рассылки
  async sendEmails(batchSize = 100) {
    try {
      await trpc.services.sendBulkEmail.mutate({ batchSize })
    } catch (error) {
      console.error('Failed to send emails:', error)
      throw error
    }
  }
}
