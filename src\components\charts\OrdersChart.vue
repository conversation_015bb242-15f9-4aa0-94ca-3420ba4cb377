<script setup lang="ts">
import { ref } from 'vue'
import Chart from 'primevue/chart'

// import InputSwitch from 'primevue/inputswitch';

const props = defineProps({
  ordersCounters: {},
  prev_ordersCounters: undefined,
  title: '',
  prev: false
})

//console.log('Total orders PROPS: ', props)
// //console.log('props.prev', props.prev);

let options = ref(),
  series = ref(),
  AChart = ref(),
  showLastYear = ref(false),
  ordersCounters = ref(props.ordersCounters),
  prev_ordersCounters = ref(props.prev_ordersCounters),
  loading = ref(true),
  title = ref(props.title)

const basicData = ref({
  labels: Object.keys(ordersCounters.value),
  datasets: [
    {
      label: 'Сейчас',
      backgroundColor: '#475569',
      data: Object.values(ordersCounters.value)
    },
    {
      label: new Date().getFullYear() - 1,
      backgroundColor: '#cbd5e1',
      data: Object.values(prev_ordersCounters.value)
    }
  ]
})

const horizontalOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  indexAxis: 'x',
  plugins: {
    legend: {
      labels: {
        color: '#495057'
      }
    }
  },
  scales: {
    x: {
      ticks: {
        color: '#495057'
      },
      grid: {
        color: '#ebedef'
      }
    },
    y: {
      ticks: {
        color: '#495057'
      },
      grid: {
        color: '#ebedef'
      }
    }
  }
})
</script>

<template>
  <div>
    <h1 class="text-gray-500 dark:text-gray-200 font-semibold">Выполненные заказы</h1>
    <Chart :canvasProps="{ height: 280 }" type="bar" :data="basicData" :options="horizontalOptions" />
  </div>
</template>
