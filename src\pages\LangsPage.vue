<script setup lang="ts">
import { trpc } from '@/tRPC'
import { useMutation, useQuery } from '@tanstack/vue-query'
import Column from 'primevue/column'
import DataTable from 'primevue/datatable'
import { onMounted, ref, watch } from 'vue'
import { FilterMatchMode } from 'primevue/api'
import InputText from 'primevue/inputtext'
import Button from 'primevue/button'
import ConfirmPopup from 'primevue/confirmpopup'
import { useConfirm } from 'primevue/useconfirm'
import { useToast } from 'primevue/usetoast'
import ConfirmDialog from 'primevue/confirmdialog'
import Dialog from 'primevue/dialog'
import Message from 'primevue/message'

import Textarea from 'primevue/textarea'

import type { Ref } from 'vue'

type RowItem = {
  id: number
  tkey: string
  en: string
  pl: string
  es: string
  it: string
  fr: string
  de: string
  ar: string
  backend: boolean
}

type LangKey = keyof RowItem

const langsKeys: Array<LangKey> = [
  //format
  'en',
  'pl',
  'es',
  'fr',
  'ar'
  // 'it',
  // 'de',
]

const confirm = useConfirm()
const toast = useToast()
const first = ref(0)
const addDialogVisible = ref(false)
const editingRows: Ref<RowItem[]> = ref([])
const data: Ref<RowItem[]> = ref([])

const counts: Ref<{ [key in LangKey]: number }> = ref({})
const newItem: Ref<RowItem> = ref({})

const {
  isLoading,
  isFetching,
  isSuccess,
  isError,
  data: rawData,
  error,
  refetch
} = useQuery({
  // queryFn: async () => await getLangDictQuery(),
  queryFn: async () => {
    const rawData: RowItem[] = await trpc.langs.getFullDict.query()

    data.value = rawData
    return rawData //.filter((i) => !editingItems[i.id])
  },
  queryKey: ['getFullDict'],
  refetchOnMount: false,
  refetchOnWindowFocus: false
})

const {
  isLoading: updateRow_isLoading,
  mutateAsync,
  isError: updateRow_isError,
  isSuccess: updateRow_isSuccess
} = useMutation({
  mutationFn: (payload: RowItem) => trpc.langs.updateRow.mutate(payload),
  mutationKey: ['updateRow']
})

const {
  isLoading: deleteRow_isLoading,
  mutateAsync: deleteAsync,
  isSuccess: deleteRow_isSuccess
} = useMutation({
  mutationFn: (payload: Pick<RowItem, 'tkey'>) => trpc.langs.deleteRow.mutate(payload),
  mutationKey: ['deleteRow']
})

const {
  isLoading: addRow_isLoading,
  mutateAsync: addAsync,
  isError: addRow_isError,
  isSuccess: addRow_isSuccess
} = useMutation({
  mutationFn: (payload: RowItem) => trpc.langs.addRow.mutate(payload),
  mutationKey: ['deleteRow']
})

const filters = ref({
  global: { value: null, matchMode: FilterMatchMode.CONTAINS },
  tkey: { value: null, matchMode: FilterMatchMode.STARTS_WITH },
  en: { value: null, matchMode: FilterMatchMode.STARTS_WITH },
  pl: { value: null, matchMode: FilterMatchMode.STARTS_WITH },
  es: { value: null, matchMode: FilterMatchMode.STARTS_WITH },
  fr: { value: null, matchMode: FilterMatchMode.STARTS_WITH },
  ar: { value: null, matchMode: FilterMatchMode.STARTS_WITH }
})

const onRowEditSave = async (event) => {
  //console.log('🚀 ~ onRowEditSave ~ event:', event)
  let { newData, index } = event

  data.value[index] = newData

  const ei: RowItem = await mutateAsync(newData)

  data.value[index] = ei //newData;

  // await refetch()
}

const onRowEditInit = async (event) => {
  // //console.log("🚀 ~ onRowEditInit ~ event:", event)
}

const delConfirm = (event, tkey) => {
  confirm.require({
    target: event.currentTarget,
    message: 'Подтвердить удаление?',
    icon: 'pi pi-exclamation-triangle',
    accept: async () => {
      // toast.add({ severity: 'info', summary: 'Confirmed', detail: 'You have accepted', life: 3000 })
      await deleteAsync({ tkey })
      refetch()
    },
    reject: () => {
      // toast.add({ severity: 'error', summary: 'Rejected', detail: 'You have rejected', life: 3000 })
    }
  })
}

function getCompletionPercent(field: LangKey) {
  if (!data.value) {
    return
  }
  // langsKeys.forEach((langKey) => ((counts.value[langKey] = data.value?.filter((row) => row[langKey])?.length || 0) / (data.value?.length || 0)) * 100)

  return (100 - (data.value.filter((row) => !row[field]).length / data.value.length) * 100).toFixed(2) + '%'
}

async function addHandler() {
  await addAsync(newItem.value)
  refetch()
}

watch(addRow_isError, () => {
  if (addRow_isError) {
    toast.add({ severity: 'error', summary: 'Ошибка', detail: '', life: 3000 })
  }
})

watch(updateRow_isError, () => {
  if (updateRow_isError) {
    toast.add({ severity: 'error', summary: 'Ошибка', detail: '', life: 3000 })
  }
})

watch(addRow_isSuccess, () => {
  if (addRow_isSuccess) {
    addDialogVisible.value = false
    toast.add({ severity: 'success', summary: 'Успешно', detail: '', life: 3000 })
  }
})

watch(updateRow_isSuccess, () => {
  if (updateRow_isSuccess) {
    // addDialogVisible.value = false
    // toast.add({ severity: 'success', summary: 'Успешно', detail: '', life: 3000 })
    refetch()
  }
})

onMounted(() => {
  try {
    document.title = 'Языки'
  } catch (error) {}
})
</script>

<template>
  <div>
    <DataTable
      table-class="table-default"
      showGridlines
      stripedRows
      v-model:editingRows="editingRows"
      editMode="row"
      dataKey="id"
      @row-edit-save="onRowEditSave"
      @row-edit-init="onRowEditInit"
      v-model:filters="filters"
      filterDisplay="row"
      :globalFilterFields="['tkey', 'en', 'pl']"
      :rowsPerPageOptions="[100, 200, 500, 1000]"
      _paginatorTemplate="RowsPerPageDropdown FirstPageLink PrevPageLink CurrentPageReport NextPageLink LastPageLink"
      currentPageReportTemplate="{first} to {last} of {totalRecords}"
      :first="first"
      :rows="100"
      paginator
      :loading="isLoading || updateRow_isLoading || addRow_isLoading"
      :value="data"
      scrollable
      scrollHeight="70vh"
      _virtualScrollerOptions="{ }"
    >
      <template #header>
        <Message v-if="isError" :closable="false" severity="error">Ошибка</Message>
        <div class="flex space-x-4 justify-end">
          <Button @click="() => (addDialogVisible = true)" icon="pi pi-plus" severity="success"></Button>
          <Button severity="primary" :loading="isFetching || isLoading" icon="pi pi-replay" text @click="refetch"></Button>

          <span class="flex items-center">
            <InputText v-model="filters['global'].value" placeholder="Поиск" />
            <i class="-ml-5 pi pi-search" />
          </span>
        </div>
      </template>
      <Column sortable field="tkey" header="Ключ">
        <template #body="{ data, field }">
          {{ data[field] }}
        </template>
        <template #editor="{ data, field }">
          <!-- <InputText v-model="data[field]" /> -->
          <Textarea v-model="data[field]" />
        </template>
      </Column>

      <Column v-for="key in langsKeys" :key="key" sortable :field="key" _header="String(key).toUpperCase()">
        <template #header="{ column }">
          <div class="!text-sm text-gray-700 dark:text-zinc-100 !flex !flex-col !justify-center">
            <div>{{ String(column.props.field).toUpperCase() }}</div>
            <div>{{ getCompletionPercent(column.props.field) }}</div>
          </div>
        </template>

        <!-- <template #filter="{field}">test</template> -->
        <!-- <template #filtericon></template> -->

        <template #body="{ data, field }">
          {{ data[field] }}
        </template>
        <template #editor="{ data, field }">
          <!-- <InputText v-model="data[field]" /> -->
          <Textarea v-model="data[field]" />
        </template>
      </Column>

      <Column :rowEditor="true" style="width: 10%; min-width: 8rem" bodyStyle="text-align:center"></Column>
      <Column header="">
        <template #body="{ data }">
          <Button @click="($event) => delConfirm($event, data.tkey)" icon="pi pi-times" rounded text severity="danger"></Button>
        </template>
      </Column>
    </DataTable>
  </div>
  <ConfirmPopup></ConfirmPopup>
  <Toast />
  <Dialog dismissableMask v-model:visible="addDialogVisible" modal header="Добавить" :style="{ width: '50rem' }" :breakpoints="{ '1199px': '75vw', '575px': '90vw' }">
    <div class="flex flex-col space-y-7 w-full py-5 px-5">
      <span class="p-float-label">
        <label class="font-semibold text-base" for="username">Ключ</label>
        <InputText v-model="newItem.tkey" class="w-full" />
      </span>
      <span v-for="key in langsKeys" :key="key" class="p-float-label">
        <label class="font-semibold text-base" for="username">{{ key }}</label>
        <InputText v-model="newItem[key]" class="w-full" />
      </span>
    </div>
    <div class="flex w-full justify-end">
      <Button :loading="addRow_isLoading" @click="addHandler">Добавить</Button>
    </div>
  </Dialog>
</template>
