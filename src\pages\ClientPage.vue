<script setup lang="ts">
import { defineProps, onMounted, ref, watch } from 'vue'
import { api } from '@/lib/_api'
// import Skeleton from 'primevue/skeleton'

import { useRoute } from 'vue-router'
import ClientCard from '@/components/client/ClientCard.vue'
import ProgressBar from 'primevue/progressbar'
import ProgressSpinner from 'primevue/progressspinner'
import ClientDebt from '@/components/client/ClientDebt.vue'
import ClientMailBox from '@/components/client/ClientMailBox.vue'

import Dialog from 'primevue/dialog';


const client = ref({})
const loading = ref(true)

const route = useRoute()


const props = defineProps({
  id: Number
})

//console.log('props:', props)

const loadClient = async () => {
  const { body } = await api('/cpan/clients/' + (props.id || route.params.id))
  client.value = body

  //console.log('client: ', client.value)
}

onMounted(async () => {
  await loadClient()
  loading.value = false

  try {
    document.title = 'Клиент ' + client.value.client_name
  } catch (error) {}
})
</script>

<template>
  <div>
    <div class="flex mx-auto" v-if="loading">
      <ProgressSpinner />
    </div>
    <div v-else>
      <ClientDebt :client="client" />
      <ClientCard :data="client" />
      <ClientMailBox :clientemail="client.client_mail" />
    </div>

  </div>
</template>
