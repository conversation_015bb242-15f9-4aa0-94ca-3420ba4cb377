<template>
  <div class="w-80 bg-gray-50 border-r border-gray-200 flex flex-col h-full">
    <!-- Заголовок -->
    <div class="p-4 border-b border-gray-200 bg-white">
      <div class="flex items-center justify-between mb-3">
        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
          <i class="pi pi-comments mr-2 text-blue-500"></i>
          Чаты с агентом
        </h2>
        <Button
          @click="handleCreateSession"
          size="small"
          severity="success"
          outlined
          class="p-2"
          v-tooltip.bottom="'Новый чат'"
          :loading="sessionsLoading.isLoading"
        >
          <i class="pi pi-plus"></i>
        </Button>
      </div>
      
      <!-- Кнопка создания новой сессии -->
      <Button
        @click="handleCreateSession"
        class="w-full"
        size="small"
        :loading="sessionsLoading.isLoading"
      >
        <i class="pi pi-plus mr-2"></i>
        Новый чат
      </Button>
    </div>

    <!-- Список сессий -->
    <div class="flex-1 overflow-y-auto">
      <!-- Индикатор загрузки -->
      <div v-if="sessionsLoading.isLoading && sessions.length === 0" class="p-4 text-center">
        <ProgressSpinner size="small" />
        <div class="text-sm text-gray-500 mt-2">Загрузка чатов...</div>
      </div>

      <!-- Сообщение об ошибке -->
      <div v-else-if="sessionsLoading.error" class="p-4">
        <Message severity="error" :closable="false">
          {{ sessionsLoading.error }}
        </Message>
        <Button
          @click="loadSessions"
          class="w-full mt-2"
          size="small"
          severity="secondary"
          outlined
        >
          <i class="pi pi-refresh mr-2"></i>
          Повторить
        </Button>
      </div>

      <!-- Пустой список -->
      <div v-else-if="sessions.length === 0" class="p-4 text-center text-gray-500">
        <i class="pi pi-inbox text-3xl mb-2 block text-gray-300"></i>
        <div class="text-sm">Нет активных чатов</div>
        <div class="text-xs text-gray-400 mt-1">Создайте новый чат для начала работы</div>
      </div>

      <!-- Список сессий -->
      <div v-else class="space-y-1 p-2">
        <SessionItem
          v-for="session in sessions"
          :key="session.id"
          :session="session"
          :is-active="session.id === currentSessionId"
          @select="setCurrentSessionId(session.id)"
          @delete="handleDeleteSession(session.id)"
        />
      </div>
    </div>

    <!-- Информация о текущей сессии -->
    <div v-if="currentSession" class="p-3 border-t border-gray-200 bg-white">
      <div class="text-xs text-gray-500">
        <div class="flex items-center justify-between">
          <span>Сообщений: {{ currentSession.messageCount }}</span>
          <span>{{ formatDate(new Date(currentSession.updatedAt)) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useAgentStore } from '@/stores/agent'
import { storeToRefs } from 'pinia'
import Button from 'primevue/button'
import ProgressSpinner from 'primevue/progressspinner'
import Message from 'primevue/message'
import SessionItem from './SessionItem.vue'

const agentStore = useAgentStore()
const {
  sessions,
  currentSessionId,
  currentSession,
  sessionsLoading
} = storeToRefs(agentStore)

const {
  loadSessions,
  createSession,
  deleteSession,
  setCurrentSessionId
} = agentStore

// Загружаем сессии при монтировании компонента
onMounted(() => {
  loadSessions()
})

const handleCreateSession = async () => {
  try {
    await createSession()
  } catch (error) {
    console.error('Ошибка создания сессии:', error)
  }
}

const handleDeleteSession = async (sessionId: string) => {
  try {
    await deleteSession(sessionId)
  } catch (error) {
    console.error('Ошибка удаления сессии:', error)
  }
}

const formatDate = (date: Date): string => {
  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 0) return 'Сегодня'
  if (diffDays === 1) return 'Вчера'
  if (diffDays < 7) return `${diffDays} дн. назад`
  return date.toLocaleDateString('ru-RU')
}
</script>

<style scoped>
/* Стили для скроллбара */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #e2e8f0;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #cbd5e1;
}
</style>
