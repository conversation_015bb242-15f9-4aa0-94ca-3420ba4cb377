<template>
  <div class="markdown-content">
    <div v-html="renderedContent" />

    <!-- PDF Preview Modal -->
    <Dialog
      v-model:visible="showPdfPreview"
      modal
      :style="{ width: '90vw', height: '90vh' }"
      :maximizable="true"
      header="Предпросмотр PDF"
      class="pdf-preview-dialog"
    >
      <div class="pdf-preview-container">
        <iframe
          v-if="currentPdfUrl"
          :src="currentPdfUrl"
          class="pdf-iframe"
          frameborder="0"
        />
        <div v-else class="pdf-error">
          <i class="pi pi-exclamation-triangle text-red-500 text-2xl mb-2"></i>
          <p>Не удалось загрузить PDF файл</p>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-between items-center w-full">
          <a
            v-if="currentPdfUrl"
            :href="currentPdfUrl"
            target="_blank"
            rel="noopener noreferrer"
            class="inline-flex items-center px-3 py-2 text-sm font-medium text-blue-600 hover:text-blue-800"
          >
            <i class="pi pi-external-link mr-2"></i>
            Открыть в новой вкладке
          </a>
          <div class="flex gap-2">
            <Button
              label="Закрыть"
              severity="secondary"
              @click="showPdfPreview = false"
            />
          </div>
        </div>
      </template>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { marked } from 'marked'
// import { mangle } from 'marked-mangle' // Отключаем mangle
import { gfmHeadingId } from 'marked-gfm-heading-id'
import Dialog from 'primevue/dialog'
import Button from 'primevue/button'


interface Props {
  content: unknown
  /** Разрешить HTML теги в markdown */
  allowHtml?: boolean
  /** Открывать ссылки в новой вкладке */
  openLinksInNewTab?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  allowHtml: false,
  openLinksInNewTab: true
})

// Реактивные переменные для PDF предпросмотра
const showPdfPreview = ref(false)
const currentPdfUrl = ref('')

// Функция для открытия PDF предпросмотра
const openPdfPreview = (url: string) => {
  currentPdfUrl.value = url
  showPdfPreview.value = true
}

// Экспозиция функции в глобальный объект для доступа из HTML
onMounted(() => {
  ;(window as any).openPdfPreview = openPdfPreview
})

onUnmounted(() => {
  if ((window as any).openPdfPreview === openPdfPreview) {
    delete (window as any).openPdfPreview
  }
})


// Универсальный рекурсивный сборщик всех текстовых полей
function extractTextRecursive(input: unknown): string[] {
  const result: string[] = []
  const candidates = ['markdown','text','message','content','value','body']
  if (typeof input === 'string') {
    result.push(input)
  } else if (Array.isArray(input)) {
    for (const item of input) {
      result.push(...extractTextRecursive(item))
    }
  } else if (typeof input === 'object' && input !== null) {
    for (const key of candidates) {
      if (typeof (input as any)[key] === 'string' && (input as any)[key].trim()) {
        result.push((input as any)[key])
      }
    }
    // Рекурсивно по всем полям
    for (const value of Object.values(input)) {
      if (typeof value === 'object' && value !== null) {
        result.push(...extractTextRecursive(value))
      }
    }
  }
  return result
}

// Функция для проверки, является ли ссылка PDF
const isPdfLink = (url: string): boolean => {
  return url.toLowerCase().includes('.pdf') || url.toLowerCase().includes('pdf')
}

// Настройка marked
const renderer = new marked.Renderer()

// Отключаем курсив (emphasis), возвращая исходный текст с символами *
renderer.em = (token: any) => token.raw;

// Sanitize hrefs to prevent XSS
const walkTokens = (token: any) => {
  if (token.type === 'link') {
    // Простая проверка для предотвращения javascript: в ссылках
    if (token.href.startsWith('javascript:')) {
      token.href = '#'
    }
  }
}

marked.use({ walkTokens })
marked.use(gfmHeadingId())
// marked.use(mangle()) // Отключаем mangle


renderer.link = function(token: any): string {
  const href = token.href || '#'
  const title = token.title || ''
  const text = token.text || ''

  const target = props.openLinksInNewTab ? ' target="_blank" rel="noopener noreferrer"' : ''
  const titleAttr = title ? ` title="${title}"` : ''

  const originalLink = `<a href="${href}"${target}${titleAttr} class="markdown-link">${text}</a>`

  if (isPdfLink(href)) {
    return `
      <span class="pdf-link-container">
        ${originalLink}
        <button
          class="pdf-preview-btn"
          onclick="window.openPdfPreview && window.openPdfPreview('${href}')"
          title="Предпросмотр PDF"
          type="button"
        >
          <i class="pi pi-eye"></i>
        </button>
      </span>
    `
  }

  return originalLink
}

marked.setOptions({
  gfm: true,
  breaks: true,
  renderer: renderer,
})

// Вычисляемое свойство для рендеринга markdown

const renderedContent = computed(() => {
  const content = props.content
  if (content == null) {
    return ''
  }

  // Преобразуем контент в строку. 
  // Если это объект или массив, он будет представлен как JSON-строка.
  const contentAsString = typeof content === 'string' ? content : JSON.stringify(content, null, 2);

  try {
    // Просто рендерим строку как Markdown.
    // Если это была строка с JSON, она будет обернута в блок кода.
    if (typeof content !== 'string' && contentAsString.startsWith('{') || contentAsString.startsWith('[')) {
        return marked.parse('```json\n' + contentAsString + '\n```');
    }
    return marked.parse(contentAsString);
  } catch (error) {
    console.error('[MarkdownRenderer] Ошибка рендеринга markdown:', error)
    // В случае ошибки показываем исходный текст как есть, в безопасном блоке.
    return `<pre class=\"markdown-error\">${contentAsString}</pre>`
  }
})
</script>

<style scoped>
.markdown-content {
  @apply text-sm leading-relaxed;
}

/* Заголовки */
.markdown-content :deep(.markdown-heading) {
  @apply font-semibold text-gray-900 mt-4 mb-2 first:mt-0;
}

.markdown-content :deep(.markdown-h1) {
  @apply text-2xl;
}

.markdown-content :deep(.markdown-h2) {
  @apply text-xl;
}

.markdown-content :deep(.markdown-h3) {
  @apply text-lg;
}

.markdown-content :deep(.markdown-h4) {
  @apply text-base;
}

.markdown-content :deep(.markdown-h5) {
  @apply text-sm;
}

.markdown-content :deep(.markdown-h6) {
  @apply text-xs;
}

/* Параграфы */
.markdown-content :deep(.markdown-paragraph) {
  @apply leading-7 mb-4 first:mt-0 last:mb-0;
}

/* Ссылки */
.markdown-content :deep(.markdown-link) {
  @apply text-blue-600 font-medium underline underline-offset-4 hover:text-blue-800 transition-colors;
}

/* Списки */
.markdown-content :deep(.markdown-list) {
  @apply my-4 ml-6;
}

.markdown-content :deep(.markdown-list-unordered) {
  @apply list-disc;
}

.markdown-content :deep(.markdown-list-ordered) {
  @apply list-decimal;
}

.markdown-content :deep(.markdown-list-item) {
  @apply mt-2;
}

/* Код */
.markdown-content :deep(.markdown-inline-code) {
  @apply bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-xs font-mono;
}

.markdown-content :deep(.markdown-code-block) {
  @apply bg-gray-100 border border-gray-200 rounded-lg p-3 my-4 overflow-x-auto;
}

.markdown-content :deep(.markdown-code) {
  @apply font-mono text-sm text-gray-800;
}

/* Таблицы */
.markdown-content :deep(.markdown-table) {
  @apply w-full border-collapse border border-gray-300 my-4;
}

.markdown-content :deep(.markdown-table-header-cell) {
  @apply border border-gray-300 px-3 py-2 bg-gray-50 font-semibold text-left;
}

.markdown-content :deep(.markdown-table-cell) {
  @apply border border-gray-300 px-3 py-2;
}

/* Blockquote */
.markdown-content :deep(.markdown-blockquote) {
  @apply border-l-4 border-gray-300 pl-4 my-4 italic text-gray-600;
}

/* Ошибки */
.markdown-content :deep(.markdown-error) {
  @apply bg-red-50 border border-red-200 text-red-700 p-3 rounded font-mono text-xs;
}

/* Горизонтальная линия */
.markdown-content :deep(hr) {
  @apply border-0 border-t border-gray-300 my-6;
}

/* Жирный и курсивный текст */
.markdown-content :deep(strong) {
  @apply font-semibold;
}

.markdown-content :deep(em) {
  @apply italic;
}

/* Изображения */
.markdown-content :deep(img) {
  @apply max-w-full h-auto rounded-lg my-4;
}

/* PDF Preview Styles */
.markdown-content :deep(.pdf-link-container) {
  @apply inline-flex items-center gap-2;
}

.markdown-content :deep(.pdf-preview-btn) {
  @apply inline-flex items-center justify-center w-6 h-6 text-xs bg-blue-100 hover:bg-blue-200 text-blue-600 rounded transition-colors cursor-pointer border-0;
}

.markdown-content :deep(.pdf-preview-btn:hover) {
  @apply bg-blue-200 text-blue-700;
}

.pdf-preview-dialog {
  @apply max-w-none;
}

.pdf-preview-container {
  @apply w-full h-full min-h-[70vh] flex items-center justify-center;
}

.pdf-iframe {
  @apply w-full h-full min-h-[70vh] border-0 rounded;
}

.pdf-error {
  @apply flex flex-col items-center justify-center text-gray-500 py-8;
}
</style>
