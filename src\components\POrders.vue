<script lang="ts" setup>
import { api } from '@/lib/_api'
import { useQuery } from '@tanstack/vue-query'
import ScrollPanel from 'primevue/scrollpanel'
import { ref } from 'vue'

const loadProblemOrders = async () => {
  const [resRti, resRumi] = await Promise.all([await api('/service/checkorders?isRumi=true'), await api('/service/checkorders')])

  return {
    rti: resRti.body,
    rumi: resRumi.body
  }
  //   return { resRti, resRumi }
}

const trnProblemOrdersKeys = (key: string) => {
  const keys: any = {
    'paid': 'Оплачен, не отправлен более 3 дней',
    'ignored': 'Не обработан более 6 дней',
    'paused': 'В ожидании оплаты более 2 дней',
    'reserved': 'В резерве более 30 дней'
  }

  return keys[key]
}

const { isLoading, isError, data, error } = useQuery({
  queryKey: ['problemOrders'],
  queryFn: async () => await loadProblemOrders(),
  refetchOnMount: false,
  refetchOnWindowFocus: true,
  refetchOnReconnect: true
})
</script>
<template>
  <span class="text-slate-600 dark:text-zinc-100 dark:text-zinc-100 font-semibold">Заказы требующие внимания</span>
  <div class="mt-3">
    <div v-if="!isLoading" class="flex justify-center gap-5 items-start">
      <div>
        <div class="text-lg font-bold">РТИ</div>
        <ScrollPanel style="width: 100%; height: 350px" class="custom mt-5">
          <div v-for="(orders, key) in data.rti" :key="key">
            <div>
              <div class="font-semibold text-slate-500 dark:text-gray-300">{{ trnProblemOrdersKeys(key) }}:</div>
            </div>
            <div class="mm-3">
              <div class="flex items-center space-x-2" v-for="(order, key) in orders" :key="key">
                <div class="w-2 h-2 rounded-full bg-yellow-500 dark:bg-orange-700"></div>
                <router-link class="hover:underline" :to="'/orders/' + order.order_id">{{ order.order_id }}</router-link>
              </div>
              <hr class="my-2" />
            </div>
          </div>
        </ScrollPanel>
      </div>

      <div class="mb-3">
        <div class="text-lg font-bold">Rumisota</div>
        <ScrollPanel style="width: 100%; height: 350px" class="custom mt-5">
          <div v-for="(orders, key) in data.rumi" :key="key">
            <div>
              <div class="font-semibold text-slate-500 dark:text-gray-300">{{ trnProblemOrdersKeys(key) }}:</div>
            </div>
            <div class="mm-3">
              <div class="flex items-center space-x-2" v-for="(order, key) in orders" :key="key">
                <div class="w-2 h-2 rounded-full bg-yellow-500 dark:bg-orange-700"></div>
                <router-link class="hover:underline" :to="'/orders/' + order.order_id">{{ order.order_id }}</router-link>
              </div>
              <hr class="my-2" />
            </div>
          </div>
        </ScrollPanel>
      </div>
    </div>

    <div class="flex mx-auto my-20" v-else>
      <!-- <ProgressSpinner /> -->
    </div>
  </div>
</template>
