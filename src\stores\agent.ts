import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { agentApiClient } from '@/lib/agent/apiClient'
import type {
  ChatSession,
  ChatMessage,
  Message,
  LoadingState,
  ChatClient,
  StreamChunk,
  AgentResponse
} from '@/lib/interfaces/Agent'

export const useAgentStore = defineStore('agent', () => {
  // Состояние сессий
  const sessions = ref<ChatSession[]>([])
  const currentSessionId = ref<string | null>(null)
  const sessionsLoading = ref<LoadingState>({
    isLoading: false,
    error: null
  })

  // Состояние сообщений
  const messages = ref<Message[]>([])
  const messagesLoading = ref<LoadingState>({
    isLoading: false,
    error: null
  })

  // Состояние отправки сообщений
  const sendingMessage = ref<LoadingState>({
    isLoading: false,
    error: null
  })

  // Состояние выбранного клиента
  const selectedClient = ref<ChatClient | null>(null)

  // Computed свойства
  const currentSession = computed(() =>
    sessions.value.find(s => s.id === currentSessionId.value)
  )

  const hasActiveSessions = computed(() => sessions.value.length > 0)

  const sortedSessions = computed(() =>
    [...sessions.value].sort((a, b) => 
      new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    )
  )

  // Действия для сессий
  const loadSessions = async () => {
    sessionsLoading.value.isLoading = true
    sessionsLoading.value.error = null

    try {
      const sessionList = await agentApiClient.getSessions()
      sessions.value = sessionList
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Ошибка загрузки сессий'
      sessionsLoading.value.error = errorMessage
      console.error('Ошибка загрузки сессий:', error)
    } finally {
      sessionsLoading.value.isLoading = false
    }
  }

  const createSession = async (): Promise<string> => {
    try {
      const session = await agentApiClient.createSession()
      await loadSessions() // Обновляем список
      currentSessionId.value = session.id
      messages.value = [] // Очищаем сообщения для новой сессии
      return session.id
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Ошибка создания сессии'
      sessionsLoading.value.error = errorMessage
      throw error
    }
  }

  const deleteSession = async (sessionId: string) => {
    try {
      await agentApiClient.deleteSession(sessionId)
      await loadSessions()

      // Если удаляем текущую сессию, сбрасываем состояние
      if (currentSessionId.value === sessionId) {
        currentSessionId.value = null
        messages.value = []
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Ошибка удаления сессии'
      sessionsLoading.value.error = errorMessage
      throw error
    }
  }

  const setCurrentSessionId = (sessionId: string | null) => {
    currentSessionId.value = sessionId
    if (sessionId) {
      loadMessageHistory(sessionId)
    } else {
      messages.value = []
    }
  }

  // Действия для сообщений
  const loadMessageHistory = async (threadId: string) => {
    messagesLoading.value.isLoading = true
    messagesLoading.value.error = null

    try {
      const history = await agentApiClient.getMessages(threadId)
      
      // Преобразуем ChatMessage в Message для UI
      const formattedMessages: Message[] = history.map(msg => ({
        id: msg.id,
        role: msg.role as 'user' | 'assistant',
        content: typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content),
        timestamp: new Date(msg.timestamp),
        files: msg.files?.map(file => ({
          url: file.url,
          fileName: file.fileName,
          type: file.type
        })),
        toolCalls: msg.toolCalls?.map((call: any) => ({
          displayId: `${call.toolCallId || call.id}-history`, // ID для истории
          toolCallId: call.toolCallId || call.id, // Fallback for older data
          toolName: call.toolName,
          input: call.input || call.args,
          output: call.output || call.result,
          status: 'success' // Assume success for history
        }))
      }))

      messages.value = formattedMessages
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Ошибка загрузки истории'
      messagesLoading.value.error = errorMessage
      console.error('Ошибка загрузки истории:', error)
    } finally {
      messagesLoading.value.isLoading = false
    }
  }

  const _saveServiceMessage = async (content: string, metadata: Record<string, any>) => {
    let sessionId = currentSessionId.value

    // Создаем новую сессию если её нет
    if (!sessionId) {
      sessionId = await createSession()
    }

    const res = await agentApiClient.saveServiceMessage(sessionId, {
      type: 'system_note',
      content,
      metadata
    })

  }

  const sendMessage = async (
    content: string,
    attachments?: File[]
  ): Promise<AgentResponse> => {
    sendingMessage.value.isLoading = true
    sendingMessage.value.error = null

    try {
      let sessionId = currentSessionId.value

      // Создаем новую сессию если её нет
      if (!sessionId) {
        sessionId = await createSession()
      }

      // Добавляем сообщение пользователя в UI
      const userMessage: Message = {
        id: Date.now().toString(),
        role: 'user',
        content,
        timestamp: new Date(),
      }
      messages.value.push(userMessage)

      // Создаем пустое сообщение агента для стриминга
      const assistantMessageId = (Date.now() + 1).toString()
      const assistantMessage: Message = {
        id: assistantMessageId,
        role: 'assistant',
        content: '',
        timestamp: new Date(),
      }
      messages.value.push(assistantMessage)

      // Используем стриминг вместо обычного запроса
      const streamGenerator = agentApiClient.streamMessage({
        message: content,
        threadId: sessionId,
      })

      let fullContent = ''
      
      // Обрабатываем стрим
      for await (const chunk of streamGenerator) {
        if(chunk.type === 'text') {
            fullContent += chunk.content
            const messageIndex = messages.value.findIndex(m => m.id === assistantMessageId)
            if (messageIndex !== -1) {
              messages.value[messageIndex].content = fullContent
            }
        } else if (chunk.type === 'tool-start') {
          const messageIndex = messages.value.findIndex(m => m.id === assistantMessageId);
          if (messageIndex !== -1) {
            if (!messages.value[messageIndex].toolCalls) {
              messages.value[messageIndex].toolCalls = [];
            }
            messages.value[messageIndex].toolCalls!.push({
              ...chunk.toolCall,
              displayId: `${chunk.toolCall.toolCallId}-input`, // Уникальный ID для отображения
              output: undefined,
            });
          }
        } else if (chunk.type === 'tool-output') {
          const messageIndex = messages.value.findIndex(m => m.id === assistantMessageId);
          // Находим исходный tool-call, чтобы получить toolName
          const originalToolCall = messages.value
            .flatMap(m => m.toolCalls || [])
            .find(tc => tc.toolCallId === chunk.toolCall.toolCallId);

          if (messageIndex !== -1 && originalToolCall) {
            if (!messages.value[messageIndex].toolCalls) {
              messages.value[messageIndex].toolCalls = [];
            }
            // Добавляем новый toolCall для результата
            messages.value[messageIndex].toolCalls!.push({
              displayId: `${chunk.toolCall.toolCallId}-output`, // Уникальный ID
              toolCallId: chunk.toolCall.toolCallId,
              toolName: originalToolCall.toolName, // Берем имя из исходного вызова
              output: chunk.toolCall.output,
              status: 'success',
            });

            // Можно опционально удалить старый spinner-only toolCall, если он был
            const inputCallIndex = messages.value[messageIndex].toolCalls!.findIndex(
              (tc) => tc.displayId === `${chunk.toolCall.toolCallId}-input`
            );
            if (inputCallIndex !== -1) {
               // Просто меняем статус, чтобы спиннер пропал
               messages.value[messageIndex].toolCalls![inputCallIndex].status = 'success';
            }
          }
        } else if (chunk.type === 'error') {
            const messageIndex = messages.value.findIndex(m => m.id === assistantMessageId)
            if(messageIndex !== -1) {
                messages.value[messageIndex].content = `Ошибка: ${chunk.error}`
            }
            break
        }
      }

      // Поскольку стриминг теперь обрабатывает все события (текст, tool-calls),
      // блок кода для повторной выборки истории был удален.
      // Это устраняет проблему перезаписи данных, полученных в реальном времени,
      // и решает проблему с исчезновением отображения вызовов инструментов.
      const finalAssistantMessage = messages.value.find(m => m.id === assistantMessageId);
        
      return {
        message: finalAssistantMessage?.content || '',
        threadId: sessionId,
        toolCalls: finalAssistantMessage?.toolCalls || [],
        files: finalAssistantMessage?.files || [] 
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Ошибка отправки сообщения'
      sendingMessage.value.error = errorMessage
      console.error('Ошибка отправки сообщения:', error)

      // Удаляем пустое сообщение агента при ошибке
      const assistantMessageIndex = messages.value.findIndex(m => m.role === 'assistant' && m.content === '')
      if (assistantMessageIndex !== -1) {
        messages.value.splice(assistantMessageIndex, 1)
      }

      throw error
    } finally {
      sendingMessage.value.isLoading = false
    }
  }

  // Очистка ошибок
  const clearErrors = () => {
    sessionsLoading.value.error = null
    messagesLoading.value.error = null
    sendingMessage.value.error = null
  }

  // Сброс состояния
  const resetState = () => {
    sessions.value = []
    currentSessionId.value = null
    messages.value = []
    clearErrors()
  }

  return {
    // Состояние
    sessions: sortedSessions,
    currentSessionId,
    currentSession,
    messages,
    hasActiveSessions,
    
    // Состояния загрузки
    sessionsLoading,
    messagesLoading,
    sendingMessage,
    
    // Действия
    loadSessions,
    createSession,
    deleteSession,
    setCurrentSessionId,
    loadMessageHistory,
    sendMessage,
    clearErrors,
    resetState,
    _saveServiceMessage
  }
})
