//import type { Request } from '@sveltejs/kit';

interface ApiParams {
  method: string
  headers: object
  body?: object | any
  data?: any
  session?: object | any
  credentials?: string
}

export async function api(resource, { method = 'GET', data = undefined, headers = {}, form = false, blob = false } = {}) {
  //import.meta.env.DEV = false
  //import.meta.env.PROD = true

  //const base = import.meta.env.DEV ? import.meta.env['VITE_APP_URL'] + ':' + import.meta.env['VITE_APP_PORT'] : import.meta.env['VITE_API_URL'];

  headers['Authorization'] = 'Bearer ' + window.localStorage.getItem('token')

  const base = import.meta.env.DEV ? import.meta.env['VITE_API_URL_DEV'] : import.meta.env['VITE_API_URL']

  const params: ApiParams = { method, headers }
  params.credentials = 'include'

  if (!form) params.headers['Content-Type'] = 'application/json'

  if (data) {
    params.method = 'POST'
    params.body = form ? data : JSON.stringify(data)
  }

  let url = `${base}/${resource}`.replace(/\/{2,}/gm, '/').replace(/(http|https):/gm, '$&/')

  if (import.meta.env.PROD) {
    url = url.replace(/api(-v2)?(?=\/)\//gm, '')
  }

  const res = await fetch(url, params)

  if (!res.ok) {
    let jres

    try {
      jres = await res.json()
    } catch (error) {}

    return {
      status: res.status,
      headers: {
        location: '/'
      },
      statusText: res.statusText,
      message: jres?.message || '',
      body: undefined
    }
  }

  let body

  try {
    body = blob ? await res.blob() : await res.json()
  } catch (e) {
    console.error('api error::res: ', res)
    console.error('api::json(): ', e)
  }

  return {
    status: res.status,
    body,
    ok: true
  }
}
