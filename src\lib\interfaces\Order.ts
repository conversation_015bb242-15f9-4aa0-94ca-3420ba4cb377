import type Product from './Product'

export interface OrderMeta {
  sum: number
  discountValue: number
  whosalePrices: boolean
  defSum: number
  bigDiscount: boolean
}

export interface OrderCoupons {
  discountVal: number
  personal: number
}

export interface OrderItem {
  ID: number
  items_order_id: number
  item_id: number
  item_count: number
  cancelgtd: number
  timestamp: string
  created_at: string
  updated_at: string
  prod_id: number
  prod_images: string
  prod_analogsku: string
  prod_sku: string
  prod_cat: string
  prod_morecats: string
  prod_price: number
  prod_count: number
  prod_manuf: string
  prod_year: string
  prod_model: string
  prod_type: string
  prod_uses: string
  prod_size: string
  prod_discount: number
  prod_purchasing: string
  prod_purpose: string
  prod_material: string
  prod_weight: string
  prod_group: string
  prod_group_count: number
  prod_group_price: string
  prod_minalert: number
  prod_coeff: string
  prod_cell: string
  prod_gtd_alert?: any
  prod_group_gtd_qty: string
  prod_gtd_qty?: any
  size_in: number
  size_in_2: number
  size_out: number
  size_out_2: number
  size_h: number
  size_h_2: number
  prod_buy_limit: number
  prod_supplier: string
  gtd: number
  orderCount: number
}

export interface Client {
  client_name: string
  client_mail: string
  client_phone: string
  client_city: string
  client_country: string
  client_street: string
  client_house: string
  client_flat: string
  client_postindex: string
  password: string
  client_number: number
  client_id: number
}

export interface SnaphotBody {
  order_id: number
  order_datetime: string
  order_lastupdate: string
  order_lastupdate_person: string
  order_status: string
  order_client: string
  order_shipping: string
  order_payment: string
  order_price: number
  order_endprice: number
  order_shippingprice: number
  order_desc: string
  order_company: string
  order_clienttype: string
  order_locale: string
  order_coupons: OrderCoupons
  order_notice: string
  order_gtd: number
  order_tracknumber: string
  items: OrderItem[]
  defsum: number
  whosalePrices: boolean
  client: Client
  emailLog?: any[]
}

export interface Snapshot {
  id: number
  orderid: number
  body: SnaphotBody
  user: string
  created_at: Date
  updated_at: Date
}

export interface Order {
  meta: OrderMeta
  data: SnaphotBody
  snapshots: Snapshot[]
}

export interface StockUpdateError {
  stockUpdateErrors: StockUpdateErrorItem[]
}

export interface StockUpdateErrorItem {
  operation: string
  product: Product
  type: string
  value: number
  orderItem: OrderItem
  currentStock: number
}
