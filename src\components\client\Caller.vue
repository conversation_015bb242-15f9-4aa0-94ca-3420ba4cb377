<script setup lang="ts">
import { ref, defineProps, watch, onMounted } from 'vue'
import InputNumber from 'primevue/inputnumber'
import { api } from '../../lib/_api'
import Button from 'primevue/button'
import InputText from 'primevue/inputtext'
import ProgressBar from 'primevue/progressbar'
import Dropdown from 'primevue/dropdown'
import Divider from 'primevue/divider'
import { useToast } from 'primevue/usetoast'

export interface ISipList {
  status: string
  pbx_id: number
  numbers: number[]
}

const props = defineProps({
  phone: String
})

const emit = defineEmits(['onSuccess'])
const toast = useToast()

const phone = ref(String(props.phone).replace(/\D*/gm, ''))
const from = ref(101)
const zadarmaError = ref(false)
const sipList = ref<ISipList>()

watch(
  () => props.phone,
  (value, oldValue) => {
    phone.value = String(value)?.replace(/\D*/gm, '')
  }
)

const loading = ref(false)

async function callClient() {
  loading.value = true
  const res = await api('/service/zadarma', {
    method: 'POST',
    data: {
      method: '/v1/request/callback/',
      payload: {
        from: from.value,
        to: phone.value,
        sip: from.value
        // predicted: false
      }
    }
  })

  if (res.ok && res.body.status === 'success') {
    emit('onSuccess')
    loading.value = false
  }

  toast.add({
    severity: 'info',
    summary: 'Для начала звонка клиенту, необходимо снять трубку на выбранном апарате',
    life: 7000
  })
}

onMounted(async () => {
  const res = await api('/service/zadarma', {
    method: 'POST',
    data: {
      method: '/v1/pbx/internal/'
      // payload: ''
    }
  })

  if (res.ok) {
    sipList.value = res.body
  } else {
    zadarmaError.value = true
  }

  //console.log('sipList:', sipList.value)

  // 
})
</script>

<template>
  <div class="mt-3 space-y-3">
    <ProgressBar v-if="loading" class="h-2" mode="indeterminate" />
    <div>
      <span class="el-text mr-2">Аппарат: </span>
      <Dropdown v-model="from" :options="sipList?.numbers || []" placeholder="Выбрать" />
      <Divider layout="vertical" />
      <InputText v-show="zadarmaError" v-model="from" />
    </div>
    <div>
      <span class="el-text mr-2">Телефон: </span>
      <InputNumber _prefix="tel:" :use-grouping="false" v-model="phone" />
    </div>
  </div>
  <div class="flex justify-end mt-5">
    <Button :loading="loading" @click="callClient" class="p-button-success p-button-md" icon="pi pi-phone" label="Вызов" />
  </div>
</template>
