import { createRouter, createWebHistory } from 'vue-router'

import HomePage from '@/pages/HomePage.vue'
// import { useUserStore } from '@/stores/user'

// const { userData } = useUserStore()

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'Главная',
      component: HomePage,
      meta: {
        sort: 0,
        isMenuItem: true,
        icon: 'pi pi-fw pi-home'
        // transition: 'slide-right'
      }
    },
    {
      path: '/goods',
      name: 'Товары',
      component: () => import('@/pages/GoodsPage.vue'),
      meta: {
        sort: 2,
        isMenuItem: true,
        icon: 'pi pi-fw pi-check'
        // transition: 'slide-right'
      }
    },
    {
      path: '/orders',
      name: 'Заказы',
      component: () => import('@/pages/OrdersPage.vue'),
      meta: {
        sort: 2.5,
        isMenuItem: true,
        icon: 'pi pi-fw pi-cart'
        // transition: 'slide-right'
      }
    },
    {
      path: '/orders/:id',
      name: 'Заказ',
      component: () => import('@/pages/OrderPage.vue'),
      meta: {
        sort: 0,
        isMenuItem: false
      }
    },
    {
      path: '/files',
      name: 'Файлы',
      component: () => import('@/pages/FilesPage.vue'),
      meta: {
        sort: 3,
        isMenuItem: true,
        icon: 'pi pi-fw pi-file'
        // transition: 'slide-right'
      }
    },
    {
      path: '/printforms',
      name: 'Печатные формы',
      component: () => import('@/pages/PrintFormsPage.vue'),
      meta: {
        sort: 4,
        isMenuItem: true,
        icon: 'pi pi-fw pi-file'
        // transition: 'slide-right'
      }
    },
    {
      path: '/products/:id',
      name: 'Товар',
      component: () => import('@/pages/ProductPage.vue'),
      meta: {
        isMenuItem: false
        // transition: 'slide-right'
      }
    },
    {
      path: '/clients/',
      name: 'Клиенты',
      component: () => import('@/pages/ClientsPage.vue'),
      meta: {
        sort: 4,
        isMenuItem: true
        // transition: 'slide-right'
      }
    },
    {
      path: '/clients/:id',
      name: 'Клиент',
      component: () => import('@/pages/ClientPage.vue'),
      meta: {
        isMenuItem: false
        // transition: 'slide-right'
      }
    },
    {
      path: '/stats/',
      name: 'Статистика',
      component: () => import('@/pages/StatsPage.vue'),
      meta: {
        sort: 5,
        isMenuItem: true
        // transition: 'slide-right'
      }
    },
    {
      path: '/users/',
      name: 'Пользователи',
      component: () => import('@/pages/UsersPage.vue'),
      meta: {
        sort: 7,
        isMenuItem: true,
        isSU: true
        // transition: 'slide-right'
      }
    },

    {
      path: '/langs/',
      name: 'Языки',
      component: () => import('@/pages/LangsPage.vue'),
      meta: {
        sort: 8,
        isMenuItem: true,
        isSU: false
        // transition: 'slide-right'
      }
    },
    {
      path: '/emailcenter',
      name: 'Рассылки',
      component: () => import('@/pages/EmailCenterPage.vue'),
      meta: {
        sort: 5,
        isMenuItem: true,
        icon: 'pi pi-fw pi-envelope'
      }
    },
    {
      path: '/settings',
      name: 'Настройки',
      component: () => import('@/pages/SettingsPage.vue'),
      meta: {
        sort: 99,
        isMenuItem: true,
        icon: 'pi pi-fw pi-cog'
      }
    },
    {
      path: '/settings/chunk',
      name: 'Редактор чанка',
      component: () => import('@/pages/HtmlChunkEditorPage.vue'),
      meta: {
        isMenuItem: false
      }
    },
    {
      path: '/agent',
      name: 'AI',
      component: () => import('@/pages/AgentPage.vue'),
      meta: {
        sort: 99,
        isMenuItem: true,
        icon: 'pi pi-fw pi-robot'
      }
    },

  ]
})

export default router
