{"name": "rti-admin", "version": "0.8.2", "private": true, "author": "xfer", "scripts": {"dev": "vite", "build-check": "run-p type-check build-only", "preview": "vite preview", "build": "vite build", "type-check": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@codemirror/lang-html": "^6.4.5", "@codemirror/lang-json": "^6.0.1", "@codemirror/theme-one-dark": "^6.1.2", "@mastra/client-js": "^0.10.10", "@overlapmedia/imagemapper": "^1.0.13", "@tanstack/vue-query": "^4.32.0", "@tiptap/core": "^3.1.0", "@tiptap/extension-blockquote": "^3.1.0", "@tiptap/extension-bold": "^3.1.0", "@tiptap/extension-color": "^3.1.0", "@tiptap/extension-horizontal-rule": "^3.1.0", "@tiptap/extension-image": "^3.1.0", "@tiptap/extension-italic": "^3.1.0", "@tiptap/extension-link": "^3.1.0", "@tiptap/extension-strike": "^3.1.0", "@tiptap/extension-table": "^3.1.0", "@tiptap/extension-table-cell": "^3.1.0", "@tiptap/extension-table-header": "^3.1.0", "@tiptap/extension-table-row": "^3.1.0", "@tiptap/extension-text-align": "^3.1.0", "@tiptap/extension-text-style": "^3.1.0", "@tiptap/extension-underline": "^3.1.0", "@tiptap/pm": "^3.1.0", "@tiptap/starter-kit": "^3.1.0", "@tiptap/vue-3": "^3.1.0", "@trpc/client": "^11.0.0-rc.593", "@trpc/server": "^11.0.0-rc.593", "@types/marked": "^5.0.2", "apexcharts": "^3.42.0", "chart.js": "^4.2.0", "codemirror": "^6.0.1", "dayjs": "^1.11.7", "dotenv": "^10.0.0", "echarts": "^5.4.3", "fabric": "^5.3.0", "flowbite-typography": "^1.0.5", "js-cookie": "^3.0.5", "marked": "^16.0.0", "marked-gfm-heading-id": "^4.1.2", "marked-mangle": "^1.1.11", "pinia": "^2.0.28", "primeicons": "^6.0.1", "primereact": "^10.3.3", "primevue": "^3.53.0", "qrcode": "^1.5.4", "quill": "^1.3.7", "socket.io-client": "^4.4.0", "tui-image-editor": "^3.15.3", "vue": "^3.2.45", "vue-codemirror": "^6.1.1", "vue-echarts": "^6.6.1", "vue-router": "^4.1.6", "vue3-apexcharts": "^1.4.4", "xlsx": "^0.17.4", "zod": "^3.22.4"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.4", "@types/js-cookie": "^3.0.3", "@types/node": "^18.11.12", "@types/quill": "^2.0.10", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.13", "eslint": "^8.22.0", "eslint-plugin-vue": "^9.3.0", "http-proxy-middleware": "^2.0.1", "npm-run-all": "^4.1.5", "postcss": "^8.4.21", "prettier": "^2.7.1", "sass": "^1.58.1", "tailwindcss": "^3.2.6", "typescript": "~4.7.4", "vite": "^4.0.0", "vue-tsc": "^1.0.12"}}